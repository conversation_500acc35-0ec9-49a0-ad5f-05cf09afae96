# Development Dockerfile for Go backend API
FROM golang:1.24.1-alpine as builder

WORKDIR /app

# Copy database module and backend code
COPY . .

# Download dependencies
RUN go mod download

# Build the application
RUN go build -o etuto-backend .

FROM alpine:latest

WORKDIR /app

# Copy the built application from the previous stage
COPY --from=builder /app/etuto-backend .

# Copy database module for runtime access
COPY --from=builder /app/database /app/database

EXPOSE 8080

# Start the application
CMD ["./etuto-backend"]
