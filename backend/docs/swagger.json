{"swagger": "2.0", "info": {"description": "API cho Hệ thống eTuto", "title": "<PERSON><PERSON> thống <PERSON> và Thi trực tuyến eTuto", "contact": {"name": "API Support", "email": "<EMAIL>"}, "version": "1.0"}, "paths": {"/api/admin/users": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all users", "produces": ["application/json"], "tags": ["admin"], "summary": "Get all users", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new user (Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Create user", "parameters": [{"description": "User details", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateUserRequest"}}], "responses": {"201": {"description": "Created", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "409": {"description": "Conflict", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/admin/users/{id}": {"put": {"security": [{"BearerAuth": []}], "description": "Update an existing user (Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Update user", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User details", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a user (Admin only)", "produces": ["application/json"], "tags": ["admin"], "summary": "Delete user", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/admin/users/{id}/role": {"put": {"security": [{"BearerAuth": []}], "description": "Update a user's role", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Update user role", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User role", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserRoleRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/admin/users/{id}/status": {"put": {"security": [{"BearerAuth": []}], "description": "Update a user's status (active/inactive)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Update user status", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User status", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateUserStatusRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/admin/users/{user_id}/quiz-summary": {"get": {"description": "Get quiz attempt summary for a specific user", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get user quiz summary", "parameters": [{"type": "string", "description": "User ID", "name": "user_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/auth/login": {"post": {"description": "Authenticate a user and return a JWT token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Login user", "parameters": [{"description": "Login credentials", "name": "login", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.LoginRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.AuthResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/auth/register": {"post": {"description": "Register a new user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Register user", "parameters": [{"description": "Registration details", "name": "register", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.RegisterRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/handlers.AuthResponse"}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/courses": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all courses", "produces": ["application/json"], "tags": ["courses"], "summary": "Get all courses", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new course (Teacher/Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["courses"], "summary": "Create course", "parameters": [{"description": "Course details", "name": "course", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateCourseRequest"}}], "responses": {"201": {"description": "Created", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/courses/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a course by its ID", "produces": ["application/json"], "tags": ["courses"], "summary": "Get course by ID", "parameters": [{"type": "string", "description": "Course ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing course (Teacher/Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["courses"], "summary": "Update course", "parameters": [{"type": "string", "description": "Course ID", "name": "id", "in": "path", "required": true}, {"description": "Course details", "name": "course", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateCourseRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a course (Teacher/Admin only)", "produces": ["application/json"], "tags": ["courses"], "summary": "Delete course", "parameters": [{"type": "string", "description": "Course ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/enrollments": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of my enrollments", "produces": ["application/json"], "tags": ["enrollment"], "summary": "Get my enrollments", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/enrollments/{course_id}": {"post": {"security": [{"BearerAuth": []}], "description": "Enroll in a course", "produces": ["application/json"], "tags": ["enrollment"], "summary": "Enroll in course", "parameters": [{"type": "string", "description": "Course ID", "name": "course_id", "in": "path", "required": true}], "responses": {"201": {"description": "Created", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "409": {"description": "Conflict", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/lessons/course/{course_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of lessons by course ID", "produces": ["application/json"], "tags": ["lessons"], "summary": "Get lessons by course", "parameters": [{"type": "string", "description": "Course ID", "name": "course_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/lessons/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a lesson by ID", "produces": ["application/json"], "tags": ["lessons"], "summary": "Get lesson", "parameters": [{"type": "string", "description": "Lesson ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a lesson (Teacher/Admin only)", "produces": ["application/json"], "tags": ["lessons"], "summary": "Delete lesson", "parameters": [{"type": "string", "description": "Lesson ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/profile": {"get": {"security": [{"BearerAuth": []}], "description": "Get the profile of the authenticated user", "produces": ["application/json"], "tags": ["profile"], "summary": "Get user profile", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handlers.ProfileResponse"}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/progress/course/{course_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get progress for a course", "produces": ["application/json"], "tags": ["progress"], "summary": "Get course progress", "parameters": [{"type": "string", "description": "Course ID", "name": "course_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/progress/slides/{slide_id}": {"post": {"security": [{"BearerAuth": []}], "description": "Mark a slide as complete", "produces": ["application/json"], "tags": ["progress"], "summary": "Mark slide complete", "parameters": [{"type": "string", "description": "Slide ID", "name": "slide_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/categories": {"get": {"description": "Get a list of question bank categories", "produces": ["application/json"], "tags": ["question bank"], "summary": "Get question bank categories", "parameters": [{"type": "string", "description": "Course ID", "name": "course_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "Create a new question bank category", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["question bank"], "summary": "Create question bank category", "parameters": [{"description": "Category details", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateCategoryRequest"}}], "responses": {"201": {"description": "Created", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/categories/{id}": {"get": {"description": "Get a question bank category by ID", "produces": ["application/json"], "tags": ["question bank"], "summary": "Get question bank category", "parameters": [{"type": "string", "description": "Category ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "put": {"description": "Update an existing question bank category", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["question bank"], "summary": "Update question bank category", "parameters": [{"type": "string", "description": "Category ID", "name": "id", "in": "path", "required": true}, {"description": "Category details", "name": "category", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateCategoryRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"description": "Delete a question bank category", "produces": ["application/json"], "tags": ["question bank"], "summary": "Delete question bank category", "parameters": [{"type": "string", "description": "Category ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/questions": {"get": {"description": "Get a list of bank questions", "produces": ["application/json"], "tags": ["question bank"], "summary": "Get bank questions", "parameters": [{"type": "string", "description": "Category ID", "name": "category_id", "in": "query"}, {"type": "string", "description": "Difficulty level", "name": "difficulty", "in": "query"}, {"type": "string", "description": "Question type", "name": "question_type", "in": "query"}, {"type": "string", "description": "Search text or tags", "name": "search", "in": "query"}, {"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Number of items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new bank question (Creator/Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["question bank"], "summary": "Create bank question", "parameters": [{"description": "Question details", "name": "question", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateBankQuestionRequest"}}], "responses": {"201": {"description": "Created", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/questions/{id}": {"get": {"description": "Get a bank question by ID", "produces": ["application/json"], "tags": ["question bank"], "summary": "Get bank question", "parameters": [{"type": "string", "description": "Question ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "put": {"description": "Update an existing bank question (Creator/Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["question bank"], "summary": "Update bank question", "parameters": [{"type": "string", "description": "Question ID", "name": "id", "in": "path", "required": true}, {"description": "Question details", "name": "question", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateBankQuestionRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"description": "Delete a bank question (Creator/Admin only)", "produces": ["application/json"], "tags": ["question bank"], "summary": "Delete bank question", "parameters": [{"type": "string", "description": "Question ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/quiz-attempt/{attempt_id}/generate": {"post": {"description": "GenerateQuizQuestions generates questions for a quiz attempt based on quiz rules", "produces": ["application/json"], "tags": ["quiz"], "summary": "Generate quiz questions", "parameters": [{"type": "string", "description": "Quiz Attempt ID", "name": "attempt_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/quiz-attempt/{attempt_id}/questions": {"get": {"description": "Get questions for a specific quiz attempt", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get quiz attempt questions", "parameters": [{"type": "string", "description": "Quiz Attempt ID", "name": "attempt_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/quiz-rules/{id}": {"get": {"description": "Get a quiz rule by ID", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get quiz rule", "parameters": [{"type": "string", "description": "Rule ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/quiz/{quiz_id}/preview": {"get": {"description": "Preview quiz questions based on quiz rules", "produces": ["application/json"], "tags": ["quiz"], "summary": "Preview quiz questions", "parameters": [{"type": "string", "description": "Quiz ID", "name": "quiz_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/quiz/{quiz_id}/rules": {"get": {"description": "Get a list of quiz rules for a specific quiz", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get quiz rules", "parameters": [{"type": "string", "description": "Quiz ID", "name": "quiz_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "post": {"description": "Create a new quiz rule", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["quiz"], "summary": "Create quiz rule", "parameters": [{"type": "string", "description": "Quiz ID", "name": "quiz_id", "in": "path", "required": true}, {"description": "Rule details", "name": "rule", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateQuizRuleRequest"}}], "responses": {"201": {"description": "Created", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/quiz/{quiz_id}/rules/reorder": {"put": {"description": "Reorder quiz rules", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["quiz"], "summary": "Reorder quiz rules", "parameters": [{"type": "string", "description": "Quiz ID", "name": "quiz_id", "in": "path", "required": true}, {"description": "New order", "name": "order", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.ReorderQuizRulesRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/quiz/{quiz_id}/rules/{id}": {"put": {"description": "Update an existing quiz rule", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["quiz"], "summary": "Update quiz rule", "parameters": [{"type": "string", "description": "Rule ID", "name": "id", "in": "path", "required": true}, {"description": "Rule details", "name": "rule", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateQuizRuleRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"description": "Delete a quiz rule", "produces": ["application/json"], "tags": ["quiz"], "summary": "Delete quiz rule", "parameters": [{"type": "string", "description": "Rule ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/question-bank/quiz/{quiz_id}/validate": {"get": {"description": "Validate if quiz rules are valid and can generate questions", "produces": ["application/json"], "tags": ["quiz"], "summary": "Validate quiz rules", "parameters": [{"type": "string", "description": "Quiz ID", "name": "quiz_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new quiz (Teacher/Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["quiz"], "summary": "Create quiz", "parameters": [{"description": "Quiz details", "name": "quiz", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateQuizRequest"}}], "responses": {"201": {"description": "Created", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes/attempts/{attempt_id}/detail": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific quiz attempt", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get quiz attempt detail", "parameters": [{"type": "string", "description": "Quiz Attempt ID", "name": "attempt_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes/attempts/{attempt_id}/submit": {"post": {"security": [{"BearerAuth": []}], "description": "Submit a quiz attempt", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["quiz"], "summary": "Submit quiz attempt", "parameters": [{"type": "string", "description": "Quiz Attempt ID", "name": "attempt_id", "in": "path", "required": true}, {"description": "Quiz attempt details", "name": "attempt", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.SubmitQuizRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes/course/{course_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of quizzes by course ID", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get quizzes by course", "parameters": [{"type": "string", "description": "Course ID", "name": "course_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes/summary": {"get": {"description": "Get summary of all quizzes for admin dashboard", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get all quizzes summary", "responses": {"200": {"description": "OK", "schema": {}}, "500": {"description": "Internal Server Error", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a quiz by ID", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get quiz", "parameters": [{"type": "string", "description": "Quiz ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing quiz (Teacher/Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["quiz"], "summary": "Update quiz", "parameters": [{"type": "string", "description": "Quiz ID", "name": "id", "in": "path", "required": true}, {"description": "Quiz details", "name": "quiz", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.UpdateQuizRequest"}}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a quiz (Teacher/Admin only)", "produces": ["application/json"], "tags": ["quiz"], "summary": "Delete quiz", "parameters": [{"type": "string", "description": "Quiz ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes/{id}/attempts": {"get": {"description": "Get paginated list of quiz attempts for admin", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get quiz attempts list", "parameters": [{"type": "string", "description": "Quiz ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "Status filter (passed/failed)", "name": "status", "in": "query"}, {"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Number of items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes/{id}/history": {"get": {"security": [{"BearerAuth": []}], "description": "Get quiz attempt history for the current user", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get user quiz history", "parameters": [{"type": "string", "description": "Quiz ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/quizzes/{id}/stats": {"get": {"description": "Get statistics for a quiz", "produces": ["application/json"], "tags": ["quiz"], "summary": "Get quiz attempt stats", "parameters": [{"type": "string", "description": "Quiz ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/slides": {"post": {"security": [{"BearerAuth": []}], "description": "Create a new slide (Teacher/Admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["slides"], "summary": "Create slide", "parameters": [{"description": "Slide details", "name": "slide", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handlers.CreateSlideRequest"}}], "responses": {"201": {"description": "Created", "schema": {}}, "400": {"description": "Bad Request", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "403": {"description": "Forbidden", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/slides/lesson/{lesson_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of slides by lesson ID", "produces": ["application/json"], "tags": ["slides"], "summary": "Get slides by lesson", "parameters": [{"type": "string", "description": "Lesson ID", "name": "lesson_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {}}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/api/slides/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get a slide by ID", "produces": ["application/json"], "tags": ["slides"], "summary": "Get slide", "parameters": [{"type": "string", "description": "Slide ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "404": {"description": "Not Found", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}, "definitions": {"handlers.AuthResponse": {"type": "object", "properties": {"token": {"type": "string"}, "user": {"$ref": "#/definitions/models.User"}}}, "handlers.CreateBankOptionRequest": {"type": "object", "required": ["option_text", "order_index"], "properties": {"is_correct": {"type": "boolean"}, "option_text": {"type": "string"}, "order_index": {"type": "integer"}}}, "handlers.CreateBankQuestionRequest": {"type": "object", "required": ["category_id", "options", "question_text", "question_type"], "properties": {"category_id": {"type": "integer"}, "difficulty": {"$ref": "#/definitions/models.DifficultyLevel"}, "image_url": {"type": "string"}, "options": {"type": "array", "minItems": 2, "items": {"$ref": "#/definitions/handlers.CreateBankOptionRequest"}}, "points": {"type": "number"}, "question_text": {"type": "string"}, "question_type": {"$ref": "#/definitions/models.QuestionType"}, "tags": {"type": "string"}}}, "handlers.CreateCategoryRequest": {"type": "object", "required": ["name"], "properties": {"course_id": {"type": "integer"}, "description": {"type": "string"}, "name": {"type": "string"}}}, "handlers.CreateCourseRequest": {"type": "object", "required": ["title"], "properties": {"description": {"type": "string"}, "thumbnail": {"type": "string"}, "title": {"type": "string"}}}, "handlers.CreateLessonRequest": {"type": "object", "required": ["course_id", "order_index", "title"], "properties": {"course_id": {"type": "integer"}, "description": {"type": "string"}, "order_index": {"type": "integer"}, "title": {"type": "string"}}}, "handlers.CreateQuizRequest": {"type": "object", "required": ["course_id", "title"], "properties": {"allow_review": {"type": "boolean"}, "course_id": {"type": "integer"}, "description": {"type": "string"}, "max_attempts": {"description": "Quiz attempt settings", "type": "integer"}, "pass_score": {"type": "number"}, "show_correct_answer": {"type": "boolean"}, "show_results": {"type": "boolean"}, "time_limit": {"type": "integer"}, "title": {"type": "string"}, "total_questions": {"type": "integer"}}}, "handlers.CreateQuizRuleRequest": {"type": "object", "required": ["category_id", "difficulty", "percentage", "question_type"], "properties": {"category_id": {"type": "integer"}, "difficulty": {"$ref": "#/definitions/models.DifficultyLevel"}, "percentage": {"type": "number", "maximum": 100, "minimum": 0.1}, "question_type": {"$ref": "#/definitions/models.QuestionType"}}}, "handlers.CreateSlideRequest": {"type": "object", "required": ["content", "lesson_id", "order_index"], "properties": {"content": {"type": "string"}, "image_url": {"type": "string"}, "lesson_id": {"type": "integer"}, "order_index": {"type": "integer"}, "title": {"type": "string"}}}, "handlers.CreateUserRequest": {"type": "object", "required": ["email", "first_name", "last_name", "password", "role"], "properties": {"email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "password": {"type": "string", "minLength": 6}, "role": {"$ref": "#/definitions/models.UserRole"}}}, "handlers.LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "password"}}}, "handlers.ProfileResponse": {"type": "object", "properties": {"user": {"$ref": "#/definitions/models.User"}}}, "handlers.RegisterRequest": {"type": "object", "required": ["email", "first_name", "last_name", "password"], "properties": {"email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "password": {"type": "string", "minLength": 6}}}, "handlers.ReorderQuizRulesRequest": {"type": "object", "required": ["rule_orders"], "properties": {"rule_orders": {"type": "array", "items": {"type": "object", "required": ["id", "order_index"], "properties": {"id": {"type": "integer"}, "order_index": {"type": "integer"}}}}}}, "handlers.SubmitQuizRequest": {"type": "object", "required": ["answers"], "properties": {"answers": {"type": "array", "items": {"type": "object", "required": ["option_ids", "question_id"], "properties": {"option_ids": {"type": "array", "items": {"type": "integer"}}, "question_id": {"type": "integer"}}}}}}, "handlers.UpdateBankQuestionRequest": {"type": "object", "properties": {"category_id": {"type": "integer"}, "difficulty": {"$ref": "#/definitions/models.DifficultyLevel"}, "image_url": {"type": "string"}, "options": {"type": "array", "items": {"$ref": "#/definitions/handlers.CreateBankOptionRequest"}}, "points": {"type": "number"}, "question_text": {"type": "string"}, "question_type": {"$ref": "#/definitions/models.QuestionType"}, "tags": {"type": "string"}}}, "handlers.UpdateCategoryRequest": {"type": "object", "properties": {"course_id": {"type": "integer"}, "description": {"type": "string"}, "name": {"type": "string"}}}, "handlers.UpdateCourseRequest": {"type": "object", "properties": {"description": {"type": "string"}, "is_published": {"type": "boolean"}, "thumbnail": {"type": "string"}, "title": {"type": "string"}}}, "handlers.UpdateLessonRequest": {"type": "object", "properties": {"description": {"type": "string"}, "order_index": {"type": "integer"}, "title": {"type": "string"}}}, "handlers.UpdateQuizRequest": {"type": "object", "properties": {"allow_review": {"type": "boolean"}, "description": {"type": "string"}, "is_published": {"type": "boolean"}, "max_attempts": {"description": "Quiz attempt settings", "type": "integer"}, "pass_score": {"type": "number"}, "show_correct_answer": {"type": "boolean"}, "show_results": {"type": "boolean"}, "time_limit": {"type": "integer"}, "title": {"type": "string"}, "total_questions": {"type": "integer"}}}, "handlers.UpdateQuizRuleRequest": {"type": "object", "properties": {"category_id": {"type": "integer"}, "difficulty": {"$ref": "#/definitions/models.DifficultyLevel"}, "order_index": {"type": "integer"}, "percentage": {"type": "number", "maximum": 100, "minimum": 0.1}, "question_type": {"$ref": "#/definitions/models.QuestionType"}}}, "handlers.UpdateUserRequest": {"type": "object", "required": ["email", "first_name", "last_name", "role"], "properties": {"email": {"type": "string"}, "first_name": {"type": "string"}, "is_active": {"type": "boolean"}, "last_name": {"type": "string"}, "role": {"$ref": "#/definitions/models.UserRole"}}}, "handlers.UpdateUserRoleRequest": {"type": "object", "required": ["role"], "properties": {"role": {"$ref": "#/definitions/models.UserRole"}}}, "handlers.UpdateUserStatusRequest": {"type": "object", "required": ["is_active"], "properties": {"is_active": {"type": "boolean"}}}, "models.Answer": {"type": "object", "properties": {"bank_option_id": {"type": "integer"}, "bank_question_id": {"type": "integer"}, "id": {"type": "integer"}, "quiz_attempt": {"description": "Relationships", "allOf": [{"$ref": "#/definitions/models.QuizAttempt"}]}, "quiz_attempt_id": {"type": "integer"}}}, "models.Category": {"type": "object", "properties": {"created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "quizzes": {"description": "Relationships", "type": "array", "items": {"$ref": "#/definitions/models.Quiz"}}, "updated_at": {"type": "string"}}}, "models.Course": {"type": "object", "properties": {"created_at": {"type": "string"}, "creator": {"description": "Relationships", "allOf": [{"$ref": "#/definitions/models.User"}]}, "creator_id": {"type": "integer"}, "description": {"type": "string"}, "enrollments": {"type": "array", "items": {"$ref": "#/definitions/models.Enrollment"}}, "id": {"type": "integer"}, "is_published": {"type": "boolean"}, "lessons": {"type": "array", "items": {"$ref": "#/definitions/models.Lesson"}}, "quizzes": {"type": "array", "items": {"$ref": "#/definitions/models.Quiz"}}, "thumbnail": {"type": "string"}, "title": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.DifficultyLevel": {"type": "string", "enum": ["easy", "normal", "hard"], "x-enum-varnames": ["DifficultyEasy", "DifficultyNormal", "DifficultyHard"]}, "models.Enrollment": {"type": "object", "properties": {"course": {"$ref": "#/definitions/models.Course"}, "course_id": {"type": "integer"}, "enrolled_at": {"type": "string"}, "id": {"type": "integer"}, "progress": {"type": "number"}, "user": {"description": "Relationships", "allOf": [{"$ref": "#/definitions/models.User"}]}, "user_id": {"type": "integer"}}}, "models.Lesson": {"type": "object", "properties": {"course": {"description": "Relationships", "allOf": [{"$ref": "#/definitions/models.Course"}]}, "course_id": {"type": "integer"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "order_index": {"type": "integer"}, "slides": {"type": "array", "items": {"$ref": "#/definitions/models.Slide"}}, "title": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.QuestionType": {"type": "string", "enum": ["single_choice", "multiple_choice"], "x-enum-varnames": ["QuestionTypeSingleChoice", "QuestionTypeMultipleChoice"]}, "models.Quiz": {"type": "object", "properties": {"allow_review": {"description": "Allow users to review their attempt history", "type": "boolean"}, "category": {"$ref": "#/definitions/models.Category"}, "category_id": {"type": "integer"}, "course": {"description": "Relationships", "allOf": [{"$ref": "#/definitions/models.Course"}]}, "course_id": {"type": "integer"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "difficulty": {"$ref": "#/definitions/models.DifficultyLevel"}, "id": {"type": "integer"}, "is_published": {"type": "boolean"}, "max_attempts": {"description": "Quiz attempt settings", "type": "integer"}, "pass_score": {"type": "number"}, "quiz_attempts": {"type": "array", "items": {"$ref": "#/definitions/models.QuizAttempt"}}, "show_correct_answer": {"description": "Show correct answers after completion", "type": "boolean"}, "show_results": {"description": "Allow users to see their results", "type": "boolean"}, "time_limit": {"description": "in minutes, 0 means no limit", "type": "integer"}, "title": {"type": "string"}, "total_questions": {"description": "Total number of questions to generate", "type": "integer"}, "updated_at": {"type": "string"}}}, "models.QuizAttempt": {"type": "object", "properties": {"answers": {"type": "array", "items": {"$ref": "#/definitions/models.Answer"}}, "completed_at": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "is_passed": {"type": "boolean"}, "max_score": {"type": "number"}, "percentage": {"type": "number"}, "quiz": {"$ref": "#/definitions/models.Quiz"}, "quiz_id": {"type": "integer"}, "score": {"type": "number"}, "started_at": {"type": "string"}, "updated_at": {"type": "string"}, "user": {"description": "Relationships", "allOf": [{"$ref": "#/definitions/models.User"}]}, "user_id": {"type": "integer"}}}, "models.Slide": {"type": "object", "properties": {"content": {"type": "string"}, "created_at": {"type": "string"}, "id": {"type": "integer"}, "image_url": {"type": "string"}, "lesson": {"description": "Relationships", "allOf": [{"$ref": "#/definitions/models.Lesson"}]}, "lesson_id": {"type": "integer"}, "order_index": {"type": "integer"}, "title": {"type": "string"}, "updated_at": {"type": "string"}}}, "models.User": {"type": "object", "properties": {"avatar": {"type": "string"}, "created_at": {"type": "string"}, "created_courses": {"description": "Relationships", "type": "array", "items": {"$ref": "#/definitions/models.Course"}}, "email": {"type": "string"}, "enrollments": {"type": "array", "items": {"$ref": "#/definitions/models.Enrollment"}}, "first_name": {"type": "string"}, "id": {"type": "integer"}, "is_active": {"type": "boolean"}, "last_name": {"type": "string"}, "quiz_attempts": {"type": "array", "items": {"$ref": "#/definitions/models.QuizAttempt"}}, "role": {"$ref": "#/definitions/models.UserRole"}, "updated_at": {"type": "string"}}}, "models.UserRole": {"type": "string", "enum": ["admin", "teacher", "student"], "x-enum-varnames": ["RoleAdmin", "<PERSON><PERSON><PERSON><PERSON>", "RoleStudent"]}}, "securityDefinitions": {"BearerAuth": {"description": "<PERSON><PERSON><PERSON> thực bằng JWT Token với tiền tố Bearer. <PERSON><PERSON> dụ: \"Bearer {token}\"", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}