definitions:
  handlers.AuthResponse:
    properties:
      token:
        type: string
      user:
        $ref: '#/definitions/models.User'
    type: object
  handlers.CreateBankOptionRequest:
    properties:
      is_correct:
        type: boolean
      option_text:
        type: string
      order_index:
        type: integer
    required:
    - option_text
    - order_index
    type: object
  handlers.CreateBankQuestionRequest:
    properties:
      category_id:
        type: integer
      difficulty:
        $ref: '#/definitions/models.DifficultyLevel'
      image_url:
        type: string
      options:
        items:
          $ref: '#/definitions/handlers.CreateBankOptionRequest'
        minItems: 2
        type: array
      points:
        type: number
      question_text:
        type: string
      question_type:
        $ref: '#/definitions/models.QuestionType'
      tags:
        type: string
    required:
    - category_id
    - options
    - question_text
    - question_type
    type: object
  handlers.CreateCategoryRequest:
    properties:
      course_id:
        type: integer
      description:
        type: string
      name:
        type: string
    required:
    - name
    type: object
  handlers.CreateCourseRequest:
    properties:
      description:
        type: string
      thumbnail:
        type: string
      title:
        type: string
    required:
    - title
    type: object
  handlers.CreateLessonRequest:
    properties:
      course_id:
        type: integer
      description:
        type: string
      order_index:
        type: integer
      title:
        type: string
    required:
    - course_id
    - order_index
    - title
    type: object
  handlers.CreateQuizRequest:
    properties:
      allow_review:
        type: boolean
      course_id:
        type: integer
      description:
        type: string
      max_attempts:
        description: Quiz attempt settings
        type: integer
      pass_score:
        type: number
      show_correct_answer:
        type: boolean
      show_results:
        type: boolean
      time_limit:
        type: integer
      title:
        type: string
      total_questions:
        type: integer
    required:
    - course_id
    - title
    type: object
  handlers.CreateQuizRuleRequest:
    properties:
      category_id:
        type: integer
      difficulty:
        $ref: '#/definitions/models.DifficultyLevel'
      percentage:
        maximum: 100
        minimum: 0.1
        type: number
      question_type:
        $ref: '#/definitions/models.QuestionType'
    required:
    - category_id
    - difficulty
    - percentage
    - question_type
    type: object
  handlers.CreateSlideRequest:
    properties:
      content:
        type: string
      image_url:
        type: string
      lesson_id:
        type: integer
      order_index:
        type: integer
      title:
        type: string
    required:
    - content
    - lesson_id
    - order_index
    type: object
  handlers.CreateUserRequest:
    properties:
      email:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      password:
        minLength: 6
        type: string
      role:
        $ref: '#/definitions/models.UserRole'
    required:
    - email
    - first_name
    - last_name
    - password
    - role
    type: object
  handlers.LoginRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      password:
        example: password
        type: string
    required:
    - email
    - password
    type: object
  handlers.ProfileResponse:
    properties:
      user:
        $ref: '#/definitions/models.User'
    type: object
  handlers.RegisterRequest:
    properties:
      email:
        type: string
      first_name:
        type: string
      last_name:
        type: string
      password:
        minLength: 6
        type: string
    required:
    - email
    - first_name
    - last_name
    - password
    type: object
  handlers.ReorderQuizRulesRequest:
    properties:
      rule_orders:
        items:
          properties:
            id:
              type: integer
            order_index:
              type: integer
          required:
          - id
          - order_index
          type: object
        type: array
    required:
    - rule_orders
    type: object
  handlers.SubmitQuizRequest:
    properties:
      answers:
        items:
          properties:
            option_ids:
              items:
                type: integer
              type: array
            question_id:
              type: integer
          required:
          - option_ids
          - question_id
          type: object
        type: array
    required:
    - answers
    type: object
  handlers.UpdateBankQuestionRequest:
    properties:
      category_id:
        type: integer
      difficulty:
        $ref: '#/definitions/models.DifficultyLevel'
      image_url:
        type: string
      options:
        items:
          $ref: '#/definitions/handlers.CreateBankOptionRequest'
        type: array
      points:
        type: number
      question_text:
        type: string
      question_type:
        $ref: '#/definitions/models.QuestionType'
      tags:
        type: string
    type: object
  handlers.UpdateCategoryRequest:
    properties:
      course_id:
        type: integer
      description:
        type: string
      name:
        type: string
    type: object
  handlers.UpdateCourseRequest:
    properties:
      description:
        type: string
      is_published:
        type: boolean
      thumbnail:
        type: string
      title:
        type: string
    type: object
  handlers.UpdateLessonRequest:
    properties:
      description:
        type: string
      order_index:
        type: integer
      title:
        type: string
    type: object
  handlers.UpdateQuizRequest:
    properties:
      allow_review:
        type: boolean
      description:
        type: string
      is_published:
        type: boolean
      max_attempts:
        description: Quiz attempt settings
        type: integer
      pass_score:
        type: number
      show_correct_answer:
        type: boolean
      show_results:
        type: boolean
      time_limit:
        type: integer
      title:
        type: string
      total_questions:
        type: integer
    type: object
  handlers.UpdateQuizRuleRequest:
    properties:
      category_id:
        type: integer
      difficulty:
        $ref: '#/definitions/models.DifficultyLevel'
      order_index:
        type: integer
      percentage:
        maximum: 100
        minimum: 0.1
        type: number
      question_type:
        $ref: '#/definitions/models.QuestionType'
    type: object
  handlers.UpdateUserRequest:
    properties:
      email:
        type: string
      first_name:
        type: string
      is_active:
        type: boolean
      last_name:
        type: string
      role:
        $ref: '#/definitions/models.UserRole'
    required:
    - email
    - first_name
    - last_name
    - role
    type: object
  handlers.UpdateUserRoleRequest:
    properties:
      role:
        $ref: '#/definitions/models.UserRole'
    required:
    - role
    type: object
  handlers.UpdateUserStatusRequest:
    properties:
      is_active:
        type: boolean
    required:
    - is_active
    type: object
  models.Answer:
    properties:
      bank_option_id:
        type: integer
      bank_question_id:
        type: integer
      id:
        type: integer
      quiz_attempt:
        allOf:
        - $ref: '#/definitions/models.QuizAttempt'
        description: Relationships
      quiz_attempt_id:
        type: integer
    type: object
  models.Category:
    properties:
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      name:
        type: string
      quizzes:
        description: Relationships
        items:
          $ref: '#/definitions/models.Quiz'
        type: array
      updated_at:
        type: string
    type: object
  models.Course:
    properties:
      created_at:
        type: string
      creator:
        allOf:
        - $ref: '#/definitions/models.User'
        description: Relationships
      creator_id:
        type: integer
      description:
        type: string
      enrollments:
        items:
          $ref: '#/definitions/models.Enrollment'
        type: array
      id:
        type: integer
      is_published:
        type: boolean
      lessons:
        items:
          $ref: '#/definitions/models.Lesson'
        type: array
      quizzes:
        items:
          $ref: '#/definitions/models.Quiz'
        type: array
      thumbnail:
        type: string
      title:
        type: string
      updated_at:
        type: string
    type: object
  models.DifficultyLevel:
    enum:
    - easy
    - normal
    - hard
    type: string
    x-enum-varnames:
    - DifficultyEasy
    - DifficultyNormal
    - DifficultyHard
  models.Enrollment:
    properties:
      course:
        $ref: '#/definitions/models.Course'
      course_id:
        type: integer
      enrolled_at:
        type: string
      id:
        type: integer
      progress:
        type: number
      user:
        allOf:
        - $ref: '#/definitions/models.User'
        description: Relationships
      user_id:
        type: integer
    type: object
  models.Lesson:
    properties:
      course:
        allOf:
        - $ref: '#/definitions/models.Course'
        description: Relationships
      course_id:
        type: integer
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      order_index:
        type: integer
      slides:
        items:
          $ref: '#/definitions/models.Slide'
        type: array
      title:
        type: string
      updated_at:
        type: string
    type: object
  models.QuestionType:
    enum:
    - single_choice
    - multiple_choice
    type: string
    x-enum-varnames:
    - QuestionTypeSingleChoice
    - QuestionTypeMultipleChoice
  models.Quiz:
    properties:
      allow_review:
        description: Allow users to review their attempt history
        type: boolean
      category:
        $ref: '#/definitions/models.Category'
      category_id:
        type: integer
      course:
        allOf:
        - $ref: '#/definitions/models.Course'
        description: Relationships
      course_id:
        type: integer
      created_at:
        type: string
      description:
        type: string
      difficulty:
        $ref: '#/definitions/models.DifficultyLevel'
      id:
        type: integer
      is_published:
        type: boolean
      max_attempts:
        description: Quiz attempt settings
        type: integer
      pass_score:
        type: number
      quiz_attempts:
        items:
          $ref: '#/definitions/models.QuizAttempt'
        type: array
      show_correct_answer:
        description: Show correct answers after completion
        type: boolean
      show_results:
        description: Allow users to see their results
        type: boolean
      time_limit:
        description: in minutes, 0 means no limit
        type: integer
      title:
        type: string
      total_questions:
        description: Total number of questions to generate
        type: integer
      updated_at:
        type: string
    type: object
  models.QuizAttempt:
    properties:
      answers:
        items:
          $ref: '#/definitions/models.Answer'
        type: array
      completed_at:
        type: string
      created_at:
        type: string
      id:
        type: integer
      is_passed:
        type: boolean
      max_score:
        type: number
      percentage:
        type: number
      quiz:
        $ref: '#/definitions/models.Quiz'
      quiz_id:
        type: integer
      score:
        type: number
      started_at:
        type: string
      updated_at:
        type: string
      user:
        allOf:
        - $ref: '#/definitions/models.User'
        description: Relationships
      user_id:
        type: integer
    type: object
  models.Slide:
    properties:
      content:
        type: string
      created_at:
        type: string
      id:
        type: integer
      image_url:
        type: string
      lesson:
        allOf:
        - $ref: '#/definitions/models.Lesson'
        description: Relationships
      lesson_id:
        type: integer
      order_index:
        type: integer
      title:
        type: string
      updated_at:
        type: string
    type: object
  models.User:
    properties:
      avatar:
        type: string
      created_at:
        type: string
      created_courses:
        description: Relationships
        items:
          $ref: '#/definitions/models.Course'
        type: array
      email:
        type: string
      enrollments:
        items:
          $ref: '#/definitions/models.Enrollment'
        type: array
      first_name:
        type: string
      id:
        type: integer
      is_active:
        type: boolean
      last_name:
        type: string
      quiz_attempts:
        items:
          $ref: '#/definitions/models.QuizAttempt'
        type: array
      role:
        $ref: '#/definitions/models.UserRole'
      updated_at:
        type: string
    type: object
  models.UserRole:
    enum:
    - admin
    - teacher
    - student
    type: string
    x-enum-varnames:
    - RoleAdmin
    - RoleTeacher
    - RoleStudent
info:
  contact:
    email: <EMAIL>
    name: API Support
  description: API cho Hệ thống eTuto
  title: Hệ thống Học và Thi trực tuyến eTuto
  version: "1.0"
paths:
  /api/admin/users:
    get:
      description: Get a list of all users
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get all users
      tags:
      - admin
    post:
      consumes:
      - application/json
      description: Create a new user (Admin only)
      parameters:
      - description: User details
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create user
      tags:
      - admin
  /api/admin/users/{id}:
    delete:
      description: Delete a user (Admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Delete user
      tags:
      - admin
    put:
      consumes:
      - application/json
      description: Update an existing user (Admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: User details
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update user
      tags:
      - admin
  /api/admin/users/{id}/role:
    put:
      consumes:
      - application/json
      description: Update a user's role
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: User role
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateUserRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update user role
      tags:
      - admin
  /api/admin/users/{id}/status:
    put:
      consumes:
      - application/json
      description: Update a user's status (active/inactive)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: User status
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateUserStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update user status
      tags:
      - admin
  /api/admin/users/{user_id}/quiz-summary:
    get:
      description: Get quiz attempt summary for a specific user
      parameters:
      - description: User ID
        in: path
        name: user_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get user quiz summary
      tags:
      - quiz
  /api/auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate a user and return a JWT token
      parameters:
      - description: Login credentials
        in: body
        name: login
        required: true
        schema:
          $ref: '#/definitions/handlers.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.AuthResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Login user
      tags:
      - auth
  /api/auth/register:
    post:
      consumes:
      - application/json
      description: Register a new user
      parameters:
      - description: Registration details
        in: body
        name: register
        required: true
        schema:
          $ref: '#/definitions/handlers.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/handlers.AuthResponse'
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Register user
      tags:
      - auth
  /api/courses:
    get:
      description: Get a list of all courses
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get all courses
      tags:
      - courses
    post:
      consumes:
      - application/json
      description: Create a new course (Teacher/Admin only)
      parameters:
      - description: Course details
        in: body
        name: course
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateCourseRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create course
      tags:
      - courses
  /api/courses/{id}:
    delete:
      description: Delete a course (Teacher/Admin only)
      parameters:
      - description: Course ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Delete course
      tags:
      - courses
    get:
      description: Get a course by its ID
      parameters:
      - description: Course ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get course by ID
      tags:
      - courses
    put:
      consumes:
      - application/json
      description: Update an existing course (Teacher/Admin only)
      parameters:
      - description: Course ID
        in: path
        name: id
        required: true
        type: string
      - description: Course details
        in: body
        name: course
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateCourseRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update course
      tags:
      - courses
  /api/enrollments:
    get:
      description: Get a list of my enrollments
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get my enrollments
      tags:
      - enrollment
  /api/enrollments/{course_id}:
    post:
      description: Enroll in a course
      parameters:
      - description: Course ID
        in: path
        name: course_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Enroll in course
      tags:
      - enrollment
  /api/lessons/{id}:
    delete:
      description: Delete a lesson (Teacher/Admin only)
      parameters:
      - description: Lesson ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Delete lesson
      tags:
      - lessons
    get:
      description: Get a lesson by ID
      parameters:
      - description: Lesson ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get lesson
      tags:
      - lessons
  /api/lessons/course/{course_id}:
    get:
      description: Get a list of lessons by course ID
      parameters:
      - description: Course ID
        in: path
        name: course_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get lessons by course
      tags:
      - lessons
  /api/profile:
    get:
      description: Get the profile of the authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ProfileResponse'
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get user profile
      tags:
      - profile
  /api/progress/course/{course_id}:
    get:
      description: Get progress for a course
      parameters:
      - description: Course ID
        in: path
        name: course_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get course progress
      tags:
      - progress
  /api/progress/slides/{slide_id}:
    post:
      description: Mark a slide as complete
      parameters:
      - description: Slide ID
        in: path
        name: slide_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Mark slide complete
      tags:
      - progress
  /api/question-bank/categories:
    get:
      description: Get a list of question bank categories
      parameters:
      - description: Course ID
        in: query
        name: course_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get question bank categories
      tags:
      - question bank
    post:
      consumes:
      - application/json
      description: Create a new question bank category
      parameters:
      - description: Category details
        in: body
        name: category
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateCategoryRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Create question bank category
      tags:
      - question bank
  /api/question-bank/categories/{id}:
    delete:
      description: Delete a question bank category
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Delete question bank category
      tags:
      - question bank
    get:
      description: Get a question bank category by ID
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get question bank category
      tags:
      - question bank
    put:
      consumes:
      - application/json
      description: Update an existing question bank category
      parameters:
      - description: Category ID
        in: path
        name: id
        required: true
        type: string
      - description: Category details
        in: body
        name: category
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateCategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Update question bank category
      tags:
      - question bank
  /api/question-bank/questions:
    get:
      description: Get a list of bank questions
      parameters:
      - description: Category ID
        in: query
        name: category_id
        type: string
      - description: Difficulty level
        in: query
        name: difficulty
        type: string
      - description: Question type
        in: query
        name: question_type
        type: string
      - description: Search text or tags
        in: query
        name: search
        type: string
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Number of items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get bank questions
      tags:
      - question bank
    post:
      consumes:
      - application/json
      description: Create a new bank question (Creator/Admin only)
      parameters:
      - description: Question details
        in: body
        name: question
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateBankQuestionRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create bank question
      tags:
      - question bank
  /api/question-bank/questions/{id}:
    delete:
      description: Delete a bank question (Creator/Admin only)
      parameters:
      - description: Question ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Delete bank question
      tags:
      - question bank
    get:
      description: Get a bank question by ID
      parameters:
      - description: Question ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get bank question
      tags:
      - question bank
    put:
      consumes:
      - application/json
      description: Update an existing bank question (Creator/Admin only)
      parameters:
      - description: Question ID
        in: path
        name: id
        required: true
        type: string
      - description: Question details
        in: body
        name: question
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateBankQuestionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Update bank question
      tags:
      - question bank
  /api/question-bank/quiz-attempt/{attempt_id}/generate:
    post:
      description: GenerateQuizQuestions generates questions for a quiz attempt based
        on quiz rules
      parameters:
      - description: Quiz Attempt ID
        in: path
        name: attempt_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Generate quiz questions
      tags:
      - quiz
  /api/question-bank/quiz-attempt/{attempt_id}/questions:
    get:
      description: Get questions for a specific quiz attempt
      parameters:
      - description: Quiz Attempt ID
        in: path
        name: attempt_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get quiz attempt questions
      tags:
      - quiz
  /api/question-bank/quiz-rules/{id}:
    get:
      description: Get a quiz rule by ID
      parameters:
      - description: Rule ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get quiz rule
      tags:
      - quiz
  /api/question-bank/quiz/{quiz_id}/preview:
    get:
      description: Preview quiz questions based on quiz rules
      parameters:
      - description: Quiz ID
        in: path
        name: quiz_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Preview quiz questions
      tags:
      - quiz
  /api/question-bank/quiz/{quiz_id}/rules:
    get:
      description: Get a list of quiz rules for a specific quiz
      parameters:
      - description: Quiz ID
        in: path
        name: quiz_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get quiz rules
      tags:
      - quiz
    post:
      consumes:
      - application/json
      description: Create a new quiz rule
      parameters:
      - description: Quiz ID
        in: path
        name: quiz_id
        required: true
        type: string
      - description: Rule details
        in: body
        name: rule
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateQuizRuleRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Create quiz rule
      tags:
      - quiz
  /api/question-bank/quiz/{quiz_id}/rules/{id}:
    delete:
      description: Delete a quiz rule
      parameters:
      - description: Rule ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Delete quiz rule
      tags:
      - quiz
    put:
      consumes:
      - application/json
      description: Update an existing quiz rule
      parameters:
      - description: Rule ID
        in: path
        name: id
        required: true
        type: string
      - description: Rule details
        in: body
        name: rule
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateQuizRuleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Update quiz rule
      tags:
      - quiz
  /api/question-bank/quiz/{quiz_id}/rules/reorder:
    put:
      consumes:
      - application/json
      description: Reorder quiz rules
      parameters:
      - description: Quiz ID
        in: path
        name: quiz_id
        required: true
        type: string
      - description: New order
        in: body
        name: order
        required: true
        schema:
          $ref: '#/definitions/handlers.ReorderQuizRulesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Reorder quiz rules
      tags:
      - quiz
  /api/question-bank/quiz/{quiz_id}/validate:
    get:
      description: Validate if quiz rules are valid and can generate questions
      parameters:
      - description: Quiz ID
        in: path
        name: quiz_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Validate quiz rules
      tags:
      - quiz
  /api/quizzes:
    post:
      consumes:
      - application/json
      description: Create a new quiz (Teacher/Admin only)
      parameters:
      - description: Quiz details
        in: body
        name: quiz
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateQuizRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create quiz
      tags:
      - quiz
  /api/quizzes/{id}:
    delete:
      description: Delete a quiz (Teacher/Admin only)
      parameters:
      - description: Quiz ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Delete quiz
      tags:
      - quiz
    get:
      description: Get a quiz by ID
      parameters:
      - description: Quiz ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get quiz
      tags:
      - quiz
    put:
      consumes:
      - application/json
      description: Update an existing quiz (Teacher/Admin only)
      parameters:
      - description: Quiz ID
        in: path
        name: id
        required: true
        type: string
      - description: Quiz details
        in: body
        name: quiz
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateQuizRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Update quiz
      tags:
      - quiz
  /api/quizzes/{id}/attempts:
    get:
      description: Get paginated list of quiz attempts for admin
      parameters:
      - description: Quiz ID
        in: path
        name: id
        required: true
        type: string
      - description: Status filter (passed/failed)
        in: query
        name: status
        type: string
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Number of items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get quiz attempts list
      tags:
      - quiz
  /api/quizzes/{id}/history:
    get:
      description: Get quiz attempt history for the current user
      parameters:
      - description: Quiz ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get user quiz history
      tags:
      - quiz
  /api/quizzes/{id}/stats:
    get:
      description: Get statistics for a quiz
      parameters:
      - description: Quiz ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get quiz attempt stats
      tags:
      - quiz
  /api/quizzes/attempts/{attempt_id}/detail:
    get:
      description: Get detailed information about a specific quiz attempt
      parameters:
      - description: Quiz Attempt ID
        in: path
        name: attempt_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get quiz attempt detail
      tags:
      - quiz
  /api/quizzes/attempts/{attempt_id}/submit:
    post:
      consumes:
      - application/json
      description: Submit a quiz attempt
      parameters:
      - description: Quiz Attempt ID
        in: path
        name: attempt_id
        required: true
        type: string
      - description: Quiz attempt details
        in: body
        name: attempt
        required: true
        schema:
          $ref: '#/definitions/handlers.SubmitQuizRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Submit quiz attempt
      tags:
      - quiz
  /api/quizzes/course/{course_id}:
    get:
      description: Get a list of quizzes by course ID
      parameters:
      - description: Course ID
        in: path
        name: course_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get quizzes by course
      tags:
      - quiz
  /api/quizzes/summary:
    get:
      description: Get summary of all quizzes for admin dashboard
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "500":
          description: Internal Server Error
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Get all quizzes summary
      tags:
      - quiz
  /api/slides:
    post:
      consumes:
      - application/json
      description: Create a new slide (Teacher/Admin only)
      parameters:
      - description: Slide details
        in: body
        name: slide
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateSlideRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema: {}
        "400":
          description: Bad Request
          schema:
            additionalProperties:
              type: string
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "403":
          description: Forbidden
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Create slide
      tags:
      - slides
  /api/slides/{id}:
    get:
      description: Get a slide by ID
      parameters:
      - description: Slide ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema: {}
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get slide
      tags:
      - slides
  /api/slides/lesson/{lesson_id}:
    get:
      description: Get a list of slides by lesson ID
      parameters:
      - description: Lesson ID
        in: path
        name: lesson_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items: {}
            type: array
        "401":
          description: Unauthorized
          schema:
            additionalProperties:
              type: string
            type: object
        "404":
          description: Not Found
          schema:
            additionalProperties:
              type: string
            type: object
      security:
      - BearerAuth: []
      summary: Get slides by lesson
      tags:
      - slides
securityDefinitions:
  BearerAuth:
    description: 'Xác thực bằng JWT Token với tiền tố Bearer. Ví dụ: "Bearer {token}"'
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
