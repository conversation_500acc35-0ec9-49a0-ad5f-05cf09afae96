package handlers

import (
	"net/http"
	"strconv"

	"etuto-database/connection"
	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

type CreateLessonRequest struct {
	CourseID    uint   `json:"course_id" binding:"required"`
	Title       string `json:"title" binding:"required"`
	Description string `json:"description"`
	OrderIndex  int    `json:"order_index" binding:"required"`
}

type UpdateLessonRequest struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	OrderIndex  *int   `json:"order_index"`
}

// @Summary Get lessons by course
// @Description Get a list of lessons by course ID
// @Tags lessons
// @Produce json
// @Security BearerAuth
// @Param course_id path string true "Course ID"
// @Success 200 {array} interface{}
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/lessons/course/{course_id} [get]
func GetLessonsByCourse(c *gin.Context) {
	courseID, err := strconv.ParseUint(c.Param("course_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var lessons []models.Lesson
	if err := connection.DB.Where("course_id = ?", courseID).
		Preload("Slides").
		Order("order_index").
		Find(&lessons).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch lessons"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"lessons": lessons})
}

// @Summary Get lesson
// @Description Get a lesson by ID
// @Tags lessons
// @Produce json
// @Security BearerAuth
// @Param id path string true "Lesson ID"
// @Success 200 {object} interface{}
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/lessons/{id} [get]
func GetLesson(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	var lesson models.Lesson
	if err := connection.DB.Preload("Course").
		Preload("Slides").
		First(&lesson, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Lesson not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"lesson": lesson})
}

// @Summary Create lesson
// @Description Create a new lesson (Teacher/Admin only)
// @Tags lessons
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param lesson body handlers.CreateLessonRequest true "Lesson details"
// @Success 201 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
func CreateLesson(c *gin.Context) {
	var req CreateLessonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if course exists and user has permission
	var course models.Course
	if err := connection.DB.First(&course, req.CourseID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	lesson := models.Lesson{
		CourseID:    req.CourseID,
		Title:       req.Title,
		Description: req.Description,
		OrderIndex:  req.OrderIndex,
	}

	if err := connection.DB.Create(&lesson).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create lesson"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"lesson": lesson})
}

// @Summary Update lesson
// @Description Update an existing lesson (Teacher/Admin only)
// @Tags lessons
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Lesson ID"
// @Param lesson body handlers.UpdateLessonRequest true "Lesson details"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
func UpdateLesson(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	var lesson models.Lesson
	if err := connection.DB.Preload("Course").First(&lesson, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Lesson not found"})
		return
	}

	// Check permission
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && lesson.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req UpdateLessonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields
	if req.Title != "" {
		lesson.Title = req.Title
	}
	if req.Description != "" {
		lesson.Description = req.Description
	}
	if req.OrderIndex != nil {
		lesson.OrderIndex = *req.OrderIndex
	}

	if err := connection.DB.Save(&lesson).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update lesson"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"lesson": lesson})
}

// @Summary Delete lesson
// @Description Delete a lesson (Teacher/Admin only)
// @Tags lessons
// @Produce json
// @Security BearerAuth
// @Param id path string true "Lesson ID"
// @Success 200 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/lessons/{id} [delete]
func DeleteLesson(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid lesson ID"})
		return
	}

	var lesson models.Lesson
	if err := connection.DB.Preload("Course").First(&lesson, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Lesson not found"})
		return
	}

	// Check permission
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && lesson.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	if err := connection.DB.Delete(&lesson).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete lesson"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Lesson deleted successfully"})
}
