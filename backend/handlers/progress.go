package handlers

import (
	"net/http"
	"strconv"
	"time"

	"etuto-database/connection"
	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

// @Summary Get course progress
// @Description Get progress for a course
// @Tags progress
// @Produce json
// @Security BearerAuth
// @Param course_id path string true "Course ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/progress/course/{course_id} [get]
func GetCourseProgress(c *gin.Context) {
	courseID, err := strconv.ParseUint(c.Param("course_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	userID, _ := c.Get("user_id")

	// Get user progress for this course
	var progress []models.UserProgress
	if err := connection.DB.Where("user_id = ? AND course_id = ?", userID, courseID).
		Preload("Lesson").
		Preload("Slide").
		Find(&progress).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch progress"})
		return
	}

	// Get course with lessons and slides to calculate overall progress
	var course models.Course
	if err := connection.DB.Preload("Lessons.Slides").
		First(&course, courseID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	// Calculate progress percentage
	totalSlides := 0
	completedSlides := 0

	for _, lesson := range course.Lessons {
		totalSlides += len(lesson.Slides)
	}

	for _, p := range progress {
		if p.IsCompleted {
			completedSlides++
		}
	}

	progressPercentage := float64(0)
	if totalSlides > 0 {
		progressPercentage = (float64(completedSlides) / float64(totalSlides)) * 100
	}

	c.JSON(http.StatusOK, gin.H{
		"course_id":           courseID,
		"total_slides":        totalSlides,
		"completed_slides":    completedSlides,
		"progress_percentage": progressPercentage,
		"progress_details":    progress,
	})
}

// @Summary Mark slide complete
// @Description Mark a slide as complete
// @Tags progress
// @Produce json
// @Security BearerAuth
// @Param slide_id path string true "Slide ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/progress/slides/{slide_id} [post]
func MarkSlideComplete(c *gin.Context) {
	slideID, err := strconv.ParseUint(c.Param("slide_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid slide ID"})
		return
	}

	userID, _ := c.Get("user_id")

	// Get slide with lesson and course info
	var slide models.Slide
	if err := connection.DB.Preload("Lesson.Course").
		First(&slide, slideID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Slide not found"})
		return
	}

	// Check if user is enrolled in the course
	var enrollment models.Enrollment
	if err := connection.DB.Where("user_id = ? AND course_id = ?", userID, slide.Lesson.Course.ID).
		First(&enrollment).Error; err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": "Not enrolled in this course"})
		return
	}

	// Check if progress already exists
	var progress models.UserProgress
	err = connection.DB.Where("user_id = ? AND slide_id = ?", userID, slideID).
		First(&progress).Error

	if err != nil {
		// Create new progress record
		progress = models.UserProgress{
			UserID:      userID.(uint),
			CourseID:    slide.Lesson.Course.ID,
			LessonID:    slide.LessonID,
			SlideID:     uint(slideID),
			IsCompleted: true,
			CompletedAt: time.Now(),
		}
		if err := connection.DB.Create(&progress).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save progress"})
			return
		}
	} else {
		// Update existing progress
		progress.IsCompleted = true
		progress.CompletedAt = time.Now()
		if err := connection.DB.Save(&progress).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update progress"})
			return
		}
	}

	// Update enrollment progress
	updateEnrollmentProgress(userID.(uint), slide.Lesson.Course.ID)

	c.JSON(http.StatusOK, gin.H{
		"message":  "Slide marked as complete",
		"progress": progress,
	})
}

func updateEnrollmentProgress(userID, courseID uint) {
	// Get total slides in course
	var totalSlides int64
	connection.DB.Table("slides").
		Joins("JOIN lessons ON slides.lesson_id = lessons.id").
		Where("lessons.course_id = ? AND slides.deleted_at IS NULL AND lessons.deleted_at IS NULL", courseID).
		Count(&totalSlides)

	// Get completed slides by user
	var completedSlides int64
	connection.DB.Model(&models.UserProgress{}).
		Where("user_id = ? AND course_id = ? AND is_completed = ?", userID, courseID, true).
		Count(&completedSlides)

	// Calculate progress percentage
	progressPercentage := float64(0)
	if totalSlides > 0 {
		progressPercentage = (float64(completedSlides) / float64(totalSlides)) * 100
	}

	// Update enrollment
	connection.DB.Model(&models.Enrollment{}).
		Where("user_id = ? AND course_id = ?", userID, courseID).
		Update("progress", progressPercentage)
}
