package handlers

import (
	"net/http"
	"strconv"

	"etuto-database/connection"
	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

// Question Bank Category handlers

type CreateCategoryRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	CourseID    *uint  `json:"course_id"`
}

type UpdateCategoryRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	CourseID    *uint  `json:"course_id"`
}

// @Summary Get question bank categories
// @Description Get a list of question bank categories
// @Tags question bank
// @Produce json
// @Param course_id query string false "Course ID"
// @Success 200 {array} interface{}
// @Failure 400 {object} map[string]string
// @Router /api/question-bank/categories [get]
func GetQuestionBankCategories(c *gin.Context) {
	var categories []models.QuestionBankCategory

	query := connection.DB.Preload("Course").Preload("BankQuestions")

	// Filter by course if provided
	if courseID := c.Query("course_id"); courseID != "" {
		if id, err := strconv.ParseUint(courseID, 10, 32); err == nil {
			query = query.Where("course_id = ? OR course_id IS NULL", uint(id))
		}
	}

	if err := query.Find(&categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch categories"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"categories": categories})
}

// @Summary Get question bank category
// @Description Get a question bank category by ID
// @Tags question bank
// @Produce json
// @Param id path string true "Category ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/categories/{id} [get]
func GetQuestionBankCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var category models.QuestionBankCategory
	if err := connection.DB.Preload("Course").Preload("BankQuestions.Options").First(&category, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"category": category})
}

// @Summary Create question bank category
// @Description Create a new question bank category
// @Tags question bank
// @Accept json
// @Produce json
// @Param category body handlers.CreateCategoryRequest true "Category details"
// @Success 201 {object} interface{}
// @Failure 400 {object} map[string]string
// @Router /api/question-bank/categories [post]
func CreateQuestionBankCategory(c *gin.Context) {
	var req CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	category := models.QuestionBankCategory{
		Name:        req.Name,
		Description: req.Description,
		CourseID:    req.CourseID,
	}

	if err := connection.DB.Create(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create category"})
		return
	}

	// Load relationships
	connection.DB.Preload("Course").First(&category, category.ID)

	c.JSON(http.StatusCreated, gin.H{"category": category})
}

// @Summary Update question bank category
// @Description Update an existing question bank category
// @Tags question bank
// @Accept json
// @Produce json
// @Param id path string true "Category ID"
// @Param category body handlers.UpdateCategoryRequest true "Category details"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/categories/{id} [put]
func UpdateQuestionBankCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	var req UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var category models.QuestionBankCategory
	if err := connection.DB.First(&category, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Update fields
	if req.Name != "" {
		category.Name = req.Name
	}
	category.Description = req.Description
	category.CourseID = req.CourseID

	if err := connection.DB.Save(&category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update category"})
		return
	}

	// Load relationships
	connection.DB.Preload("Course").First(&category, category.ID)

	c.JSON(http.StatusOK, gin.H{"category": category})
}

// @Summary Delete question bank category
// @Description Delete a question bank category
// @Tags question bank
// @Produce json
// @Param id path string true "Category ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/categories/{id} [delete]
func DeleteQuestionBankCategory(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid category ID"})
		return
	}

	// Check if category has questions
	var count int64
	connection.DB.Model(&models.BankQuestion{}).Where("category_id = ?", uint(id)).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete category with existing questions"})
		return
	}

	if err := connection.DB.Delete(&models.QuestionBankCategory{}, uint(id)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete category"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Category deleted successfully"})
}

// Bank Question handlers

type CreateBankQuestionRequest struct {
	CategoryID   uint                      `json:"category_id" binding:"required"`
	QuestionText string                    `json:"question_text" binding:"required"`
	QuestionType models.QuestionType       `json:"question_type" binding:"required"`
	ImageURL     string                    `json:"image_url"`
	Points       float64                   `json:"points"`
	Difficulty   models.DifficultyLevel    `json:"difficulty"`
	Tags         string                    `json:"tags"`
	Options      []CreateBankOptionRequest `json:"options" binding:"required,min=2"`
}

type CreateBankOptionRequest struct {
	OptionText string `json:"option_text" binding:"required"`
	IsCorrect  bool   `json:"is_correct"`
	OrderIndex int    `json:"order_index" binding:"required"`
}

type UpdateBankQuestionRequest struct {
	CategoryID   uint                      `json:"category_id"`
	QuestionText string                    `json:"question_text"`
	QuestionType models.QuestionType       `json:"question_type"`
	ImageURL     string                    `json:"image_url"`
	Points       float64                   `json:"points"`
	Difficulty   models.DifficultyLevel    `json:"difficulty"`
	Tags         string                    `json:"tags"`
	Options      []CreateBankOptionRequest `json:"options"`
}

// @Summary Get bank questions
// @Description Get a list of bank questions
// @Tags question bank
// @Produce json
// @Param category_id query string false "Category ID"
// @Param difficulty query string false "Difficulty level"
// @Param question_type query string false "Question type"
// @Param search query string false "Search text or tags"
// @Param page query int false "Page number"
// @Param limit query int false "Number of items per page"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Router /api/question-bank/questions [get]
func GetBankQuestions(c *gin.Context) {
	var questions []models.BankQuestion

	query := connection.DB.Preload("Category").Preload("Creator").Preload("Options")

	// Filter by category
	if categoryID := c.Query("category_id"); categoryID != "" {
		if id, err := strconv.ParseUint(categoryID, 10, 32); err == nil {
			query = query.Where("category_id = ?", uint(id))
		}
	}

	// Filter by difficulty
	if difficulty := c.Query("difficulty"); difficulty != "" {
		query = query.Where("difficulty = ?", difficulty)
	}

	// Filter by question type
	if questionType := c.Query("question_type"); questionType != "" {
		query = query.Where("question_type = ?", questionType)
	}

	// Search by text or tags
	if search := c.Query("search"); search != "" {
		query = query.Where("question_text ILIKE ? OR tags ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Pagination
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset := (page - 1) * limit

	var total int64
	query.Model(&models.BankQuestion{}).Count(&total)

	if err := query.Offset(offset).Limit(limit).Find(&questions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch questions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"questions": questions,
		"total":     total,
		"page":      page,
		"limit":     limit,
	})
}

// @Summary Get bank question
// @Description Get a bank question by ID
// @Tags question bank
// @Produce json
// @Param id path string true "Question ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/questions/{id} [get]
func GetBankQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	var question models.BankQuestion
	if err := connection.DB.Preload("Category").Preload("Creator").Preload("Options").First(&question, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"question": question})
}

// @Summary Create bank question
// @Description Create a new bank question (Creator/Admin only)
// @Tags question bank
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param question body handlers.CreateBankQuestionRequest true "Question details"
// @Success 201 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Router /api/question-bank/questions [post]
func CreateBankQuestion(c *gin.Context) {
	var req CreateBankQuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Validate that at least one option is correct
	hasCorrectOption := false
	for _, option := range req.Options {
		if option.IsCorrect {
			hasCorrectOption = true
			break
		}
	}
	if !hasCorrectOption {
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one option must be correct"})
		return
	}

	// Set default values
	if req.Points == 0 {
		req.Points = 1
	}
	if req.Difficulty == "" {
		req.Difficulty = models.DifficultyNormal
	}

	// Start transaction
	tx := connection.DB.Begin()

	question := models.BankQuestion{
		CategoryID:   req.CategoryID,
		QuestionText: req.QuestionText,
		QuestionType: req.QuestionType,
		ImageURL:     req.ImageURL,
		Points:       req.Points,
		Difficulty:   req.Difficulty,
		Tags:         req.Tags,
		CreatedBy:    userID.(uint),
	}

	if err := tx.Create(&question).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create question"})
		return
	}

	// Create options
	for _, optionReq := range req.Options {
		option := models.BankOption{
			BankQuestionID: question.ID,
			OptionText:     optionReq.OptionText,
			IsCorrect:      optionReq.IsCorrect,
			OrderIndex:     optionReq.OrderIndex,
		}
		if err := tx.Create(&option).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create option"})
			return
		}
	}

	tx.Commit()

	// Load relationships
	connection.DB.Preload("Category").Preload("Creator").Preload("Options").First(&question, question.ID)

	c.JSON(http.StatusCreated, gin.H{"question": question})
}

// @Summary Update bank question
// @Description Update an existing bank question (Creator/Admin only)
// @Tags question bank
// @Accept json
// @Produce json
// @Param id path string true "Question ID"
// @Param question body handlers.UpdateBankQuestionRequest true "Question details"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/questions/{id} [put]
func UpdateBankQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	var req UpdateBankQuestionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var question models.BankQuestion
	if err := connection.DB.First(&question, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
		return
	}

	// Check if user can edit this question (creator or admin)
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if question.CreatedBy != userID.(uint) && userRole.(models.UserRole) != models.RoleAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only edit your own questions"})
		return
	}

	// Validate options if provided
	if len(req.Options) > 0 {
		hasCorrectOption := false
		for _, option := range req.Options {
			if option.IsCorrect {
				hasCorrectOption = true
				break
			}
		}
		if !hasCorrectOption {
			c.JSON(http.StatusBadRequest, gin.H{"error": "At least one option must be correct"})
			return
		}
	}

	// Start transaction
	tx := connection.DB.Begin()

	// Update question fields
	if req.CategoryID != 0 {
		question.CategoryID = req.CategoryID
	}
	if req.QuestionText != "" {
		question.QuestionText = req.QuestionText
	}
	if req.QuestionType != "" {
		question.QuestionType = req.QuestionType
	}
	question.ImageURL = req.ImageURL
	if req.Points != 0 {
		question.Points = req.Points
	}
	if req.Difficulty != "" {
		question.Difficulty = req.Difficulty
	}
	question.Tags = req.Tags

	if err := tx.Save(&question).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update question"})
		return
	}

	// Update options if provided
	if len(req.Options) > 0 {
		// Delete existing options
		if err := tx.Where("bank_question_id = ?", question.ID).Delete(&models.BankOption{}).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update options"})
			return
		}

		// Create new options
		for _, optionReq := range req.Options {
			option := models.BankOption{
				BankQuestionID: question.ID,
				OptionText:     optionReq.OptionText,
				IsCorrect:      optionReq.IsCorrect,
				OrderIndex:     optionReq.OrderIndex,
			}
			if err := tx.Create(&option).Error; err != nil {
				tx.Rollback()
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create option"})
				return
			}
		}
	}

	tx.Commit()

	// Load relationships
	connection.DB.Preload("Category").Preload("Creator").Preload("Options").First(&question, question.ID)

	c.JSON(http.StatusOK, gin.H{"question": question})
}

// @Summary Delete bank question
// @Description Delete a bank question (Creator/Admin only)
// @Tags question bank
// @Produce json
// @Param id path string true "Question ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/questions/{id} [delete]
func DeleteBankQuestion(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid question ID"})
		return
	}

	var question models.BankQuestion
	if err := connection.DB.First(&question, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Question not found"})
		return
	}

	// Check if user can delete this question (creator or admin)
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if question.CreatedBy != userID.(uint) && userRole.(models.UserRole) != models.RoleAdmin {
		c.JSON(http.StatusForbidden, gin.H{"error": "You can only delete your own questions"})
		return
	}

	// Check if question is used in any quiz attempts
	var count int64
	connection.DB.Model(&models.QuizAttemptQuestion{}).Where("bank_question_id = ?", uint(id)).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete question that is used in quiz attempts"})
		return
	}

	// Start transaction
	tx := connection.DB.Begin()

	// Delete options first
	if err := tx.Where("bank_question_id = ?", uint(id)).Delete(&models.BankOption{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete question options"})
		return
	}

	// Delete question
	if err := tx.Delete(&question).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete question"})
		return
	}

	tx.Commit()

	c.JSON(http.StatusOK, gin.H{"message": "Question deleted successfully"})
}
