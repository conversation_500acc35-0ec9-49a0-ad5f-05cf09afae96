package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"etuto-database/connection"
	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

type CreateQuizRequest struct {
	CourseID       uint    `json:"course_id" binding:"required"`
	Title          string  `json:"title" binding:"required"`
	Description    string  `json:"description"`
	TimeLimit      int     `json:"time_limit"`
	PassScore      float64 `json:"pass_score"`
	TotalQuestions int     `json:"total_questions"`

	// Quiz attempt settings
	MaxAttempts       int  `json:"max_attempts"`
	ShowResults       bool `json:"show_results"`
	ShowCorrectAnswer bool `json:"show_correct_answer"`
	AllowReview       bool `json:"allow_review"`
}

type UpdateQuizRequest struct {
	Title          string   `json:"title"`
	Description    string   `json:"description"`
	TimeLimit      *int     `json:"time_limit"`
	PassScore      *float64 `json:"pass_score"`
	IsPublished    *bool    `json:"is_published"`
	TotalQuestions *int     `json:"total_questions"`

	// Quiz attempt settings
	MaxAttempts       *int  `json:"max_attempts"`
	ShowResults       *bool `json:"show_results"`
	ShowCorrectAnswer *bool `json:"show_correct_answer"`
	AllowReview       *bool `json:"allow_review"`
}

type SubmitQuizRequest struct {
	Answers []struct {
		QuestionID uint   `json:"question_id" binding:"required"`
		OptionIDs  []uint `json:"option_ids" binding:"required"`
	} `json:"answers" binding:"required"`
}

// @Summary Get quizzes by course
// @Description Get a list of quizzes by course ID
// @Tags quiz
// @Produce json
// @Security BearerAuth
// @Param course_id path string true "Course ID"
// @Success 200 {array} interface{}
// @Failure 401 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/course/{course_id} [get]
func GetQuizzesByCourse(c *gin.Context) {
	courseID, err := strconv.ParseUint(c.Param("course_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course ID"})
		return
	}

	var quizzes []models.Quiz
	query := connection.DB.Where("course_id = ?", courseID).
		Preload("Category")

	// Filter by published status for students
	userRole, _ := c.Get("user_role")
	if userRole == models.RoleStudent {
		query = query.Where("is_published = ?", true)
	}

	if err := query.Find(&quizzes).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quizzes"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"quizzes": quizzes})
}

// @Summary Get quiz
// @Description Get a quiz by ID
// @Tags quiz
// @Produce json
// @Security BearerAuth
// @Param id path string true "Quiz ID"
// @Success 200 {object} interface{}
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/{id} [get]
func GetQuiz(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var quiz models.Quiz
	query := connection.DB.Preload("Course").
		Preload("Category")

	if err := query.First(&quiz, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check if user can access this quiz
	userRole, _ := c.Get("user_role")
	if userRole == models.RoleStudent && !quiz.IsPublished {
		c.JSON(http.StatusForbidden, gin.H{"error": "Quiz not available"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"quiz": quiz})
}

// @Summary Create quiz
// @Description Create a new quiz (Teacher/Admin only)
// @Tags quiz
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param quiz body handlers.CreateQuizRequest true "Quiz details"
// @Success 201 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Router /api/quizzes [post]
func CreateQuiz(c *gin.Context) {
	var req CreateQuizRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if course exists and user has permission
	var course models.Course
	if err := connection.DB.First(&course, req.CourseID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Course not found"})
		return
	}

	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	// Set default total questions if not provided
	totalQuestions := req.TotalQuestions
	if totalQuestions <= 0 {
		totalQuestions = 10 // Default value
	}

	quiz := models.Quiz{
		CourseID:       req.CourseID,
		Title:          req.Title,
		Description:    req.Description,
		TimeLimit:      req.TimeLimit,
		PassScore:      req.PassScore,
		TotalQuestions: totalQuestions,
		IsPublished:    false,

		// Quiz attempt settings
		MaxAttempts:       req.MaxAttempts,
		ShowResults:       req.ShowResults,
		ShowCorrectAnswer: req.ShowCorrectAnswer,
		AllowReview:       req.AllowReview,
	}

	if err := connection.DB.Create(&quiz).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"quiz": quiz})
}

// @Summary Update quiz
// @Description Update an existing quiz (Teacher/Admin only)
// @Tags quiz
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "Quiz ID"
// @Param quiz body handlers.UpdateQuizRequest true "Quiz details"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/{id} [put]
func UpdateQuiz(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var quiz models.Quiz
	if err := connection.DB.Preload("Course").First(&quiz, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check permission
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && quiz.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req UpdateQuizRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update fields
	if req.Title != "" {
		quiz.Title = req.Title
	}
	if req.Description != "" {
		quiz.Description = req.Description
	}
	if req.TimeLimit != nil {
		quiz.TimeLimit = *req.TimeLimit
	}
	if req.PassScore != nil {
		quiz.PassScore = *req.PassScore
	}
	if req.IsPublished != nil {
		quiz.IsPublished = *req.IsPublished
	}
	if req.TotalQuestions != nil && *req.TotalQuestions > 0 {
		quiz.TotalQuestions = *req.TotalQuestions
	}

	// Update quiz attempt settings
	if req.MaxAttempts != nil {
		quiz.MaxAttempts = *req.MaxAttempts
	}
	if req.ShowResults != nil {
		quiz.ShowResults = *req.ShowResults
	}
	if req.ShowCorrectAnswer != nil {
		quiz.ShowCorrectAnswer = *req.ShowCorrectAnswer
	}
	if req.AllowReview != nil {
		quiz.AllowReview = *req.AllowReview
	}

	if err := connection.DB.Save(&quiz).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update quiz"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"quiz": quiz})
}

// @Summary Delete quiz
// @Description Delete a quiz (Teacher/Admin only)
// @Tags quiz
// @Produce json
// @Security BearerAuth
// @Param id path string true "Quiz ID"
// @Success 200 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/{id} [delete]
func DeleteQuiz(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var quiz models.Quiz
	if err := connection.DB.Preload("Course").First(&quiz, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check permission
	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")
	if userRole != models.RoleAdmin && quiz.Course.CreatorID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	if err := connection.DB.Delete(&quiz).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete quiz"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quiz deleted successfully"})
}

func StartQuizAttempt(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	userID, _ := c.Get("user_id")
	fmt.Printf("=== START QUIZ ATTEMPT ===\n")
	fmt.Printf("Quiz ID: %d, User ID: %d\n", id, userID.(uint))

	var quiz models.Quiz
	if err := connection.DB.First(&quiz, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	if !quiz.IsPublished {
		c.JSON(http.StatusForbidden, gin.H{"error": "Quiz not available"})
		return
	}

	// Check if there's already a pending attempt (not completed)
	var pendingAttempt models.QuizAttempt
	if err := connection.DB.Where("user_id = ? AND quiz_id = ? AND completed_at IS NULL", userID.(uint), uint(id)).
		First(&pendingAttempt).Error; err == nil {
		fmt.Printf("Found existing pending attempt ID: %d\n", pendingAttempt.ID)
		c.JSON(http.StatusOK, gin.H{"attempt": pendingAttempt})
		return
	}

	// Check if user has exceeded max attempts
	if quiz.MaxAttempts > 0 {
		var attemptCount int64
		connection.DB.Model(&models.QuizAttempt{}).
			Where("user_id = ? AND quiz_id = ?", userID.(uint), uint(id)).
			Count(&attemptCount)

		if attemptCount >= int64(quiz.MaxAttempts) {
			c.JSON(http.StatusForbidden, gin.H{
				"error":            fmt.Sprintf("Maximum attempts exceeded. You can only attempt this quiz %d times.", quiz.MaxAttempts),
				"max_attempts":     quiz.MaxAttempts,
				"current_attempts": attemptCount,
			})
			return
		}
	}

	attempt := models.QuizAttempt{
		UserID:    userID.(uint),
		QuizID:    uint(id),
		StartedAt: time.Now(),
	}

	fmt.Printf("Creating attempt: UserID=%d, QuizID=%d\n", userID.(uint), uint(id))

	if err := connection.DB.Create(&attempt).Error; err != nil {
		fmt.Printf("ERROR creating attempt: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start quiz attempt"})
		return
	}

	fmt.Printf("Successfully created attempt ID: %d\n", attempt.ID)
	c.JSON(http.StatusCreated, gin.H{"attempt": attempt})
}

// @Summary Submit quiz attempt
// @Description Submit a quiz attempt
// @Tags quiz
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param attempt_id path string true "Quiz Attempt ID"
// @Param attempt body handlers.SubmitQuizRequest true "Quiz attempt details"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/attempts/{attempt_id}/submit [post]
func SubmitQuizAttempt(c *gin.Context) {
	attemptID, err := strconv.ParseUint(c.Param("attempt_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid attempt ID"})
		return
	}

	var attempt models.QuizAttempt
	if err := connection.DB.Preload("Quiz").
		First(&attempt, attemptID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz attempt not found"})
		return
	}

	// Check if this is the user's attempt
	userID, _ := c.Get("user_id")
	if attempt.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req SubmitQuizRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Debug logging
	fmt.Printf("=== SUBMIT QUIZ DEBUG ===\n")
	fmt.Printf("Attempt ID: %d\n", attemptID)
	fmt.Printf("Received answers: %+v\n", req.Answers)

	// Get quiz attempt questions (dynamic questions from question bank)
	var attemptQuestions []models.QuizAttemptQuestion
	if err := connection.DB.Where("quiz_attempt_id = ?", attempt.ID).
		Preload("BankQuestion.Options").
		Find(&attemptQuestions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch attempt questions"})
		return
	}

	fmt.Printf("Found %d attempt questions\n", len(attemptQuestions))
	for i, aq := range attemptQuestions {
		fmt.Printf("Attempt question %d: ID=%d, BankQuestionID=%d, Points=%.2f\n", i, aq.ID, aq.BankQuestionID, aq.Points)
		fmt.Printf("  Bank question text: %s\n", aq.BankQuestion.QuestionText)
	}

	// Calculate max score from all attempt questions
	var maxScore float64
	for _, attemptQuestion := range attemptQuestions {
		maxScore += attemptQuestion.Points
	}

	// Calculate score and save answers
	var totalScore float64
	for _, answer := range req.Answers {
		// Find the attempt question by bank_question_id
		var attemptQuestion models.QuizAttemptQuestion
		var found bool
		for _, aq := range attemptQuestions {
			if aq.BankQuestionID == answer.QuestionID {
				attemptQuestion = aq
				found = true
				break
			}
		}

		if !found {
			fmt.Printf("Question not found for bank_question_id: %d\n", answer.QuestionID)
			continue // Skip if question not found
		}

		fmt.Printf("Processing answer for bank_question_id: %d, selected options: %v\n", answer.QuestionID, answer.OptionIDs)
		fmt.Printf("  Found attempt question: ID=%d, BankQuestionID=%d\n", attemptQuestion.ID, attemptQuestion.BankQuestionID)

		// Save user answers
		for _, optionID := range answer.OptionIDs {
			userAnswer := models.Answer{
				QuizAttemptID:  attempt.ID,
				BankQuestionID: answer.QuestionID, // Frontend sends bank_question_id as QuestionID
				BankOptionID:   optionID,          // Frontend sends bank_option_id as OptionID
			}
			if err := connection.DB.Create(&userAnswer).Error; err != nil {
				fmt.Printf("ERROR saving answer: %v\n", err)
			} else {
				fmt.Printf("  Saved answer: QuizAttemptID=%d, BankQuestionID=%d, BankOptionID=%d\n",
					userAnswer.QuizAttemptID, userAnswer.BankQuestionID, userAnswer.BankOptionID)
			}
		}

		// Calculate score for this question
		bankQuestion := attemptQuestion.BankQuestion
		fmt.Printf("Question type: %s, Points: %.2f\n", bankQuestion.QuestionType, attemptQuestion.Points)

		if bankQuestion.QuestionType == models.QuestionTypeSingleChoice {
			// For single choice, check if the selected option is correct
			for _, optionID := range answer.OptionIDs {
				for _, option := range bankQuestion.Options {
					fmt.Printf("Checking option %d: IsCorrect=%t\n", option.ID, option.IsCorrect)
					if option.ID == optionID && option.IsCorrect {
						totalScore += attemptQuestion.Points
						fmt.Printf("Correct answer! Added %.2f points. Total: %.2f\n", attemptQuestion.Points, totalScore)
						break
					}
				}
			}
		} else if bankQuestion.QuestionType == models.QuestionTypeMultipleChoice {
			// For multiple choice, calculate partial credit
			totalCorrectOptions := 0
			selectedCorrectOptions := 0
			selectedIncorrectOptions := 0

			// Count total correct options for this question
			for _, option := range bankQuestion.Options {
				if option.IsCorrect {
					totalCorrectOptions++
				}
			}

			// Count selected correct and incorrect options
			for _, optionID := range answer.OptionIDs {
				for _, option := range bankQuestion.Options {
					if option.ID == optionID {
						if option.IsCorrect {
							selectedCorrectOptions++
						} else {
							selectedIncorrectOptions++
						}
						break
					}
				}
			}

			// Award points only if all correct options are selected and no incorrect ones
			if selectedCorrectOptions == totalCorrectOptions && selectedIncorrectOptions == 0 {
				totalScore += attemptQuestion.Points
			}
		}
	}

	// Update attempt with results
	var percentage float64
	if maxScore > 0 {
		percentage = (totalScore / maxScore) * 100
	}

	fmt.Printf("Final results: TotalScore=%.2f, MaxScore=%.2f, Percentage=%.2f%%\n", totalScore, maxScore, percentage)

	attempt.Score = totalScore
	attempt.MaxScore = maxScore
	attempt.Percentage = percentage
	attempt.IsPassed = percentage >= attempt.Quiz.PassScore
	attempt.CompletedAt = time.Now()

	if err := connection.DB.Save(&attempt).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save quiz results"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"attempt": attempt})
}

// @Summary Get user quiz history
// @Description Get quiz attempt history for the current user
// @Tags quiz
// @Produce json
// @Security BearerAuth
// @Param id path string true "Quiz ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/{id}/history [get]
func GetUserQuizHistory(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	userID, _ := c.Get("user_id")

	// Get quiz to check if review is allowed
	var quiz models.Quiz
	if err := connection.DB.First(&quiz, uint(quizID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	if !quiz.AllowReview {
		c.JSON(http.StatusForbidden, gin.H{"error": "Review not allowed for this quiz"})
		return
	}

	var attempts []models.QuizAttempt
	query := connection.DB.Where("user_id = ? AND quiz_id = ?", userID.(uint), uint(quizID)).
		Order("created_at DESC")

	// Only include results if quiz allows showing results
	if quiz.ShowResults {
		query = query.Preload("Quiz")
	}

	if err := query.Find(&attempts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quiz history"})
		return
	}

	// Filter out sensitive data if results shouldn't be shown
	if !quiz.ShowResults {
		for i := range attempts {
			attempts[i].Score = 0
			attempts[i].MaxScore = 0
			attempts[i].Percentage = 0
			attempts[i].IsPassed = false
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"attempts": attempts,
		"quiz": gin.H{
			"id":                  quiz.ID,
			"title":               quiz.Title,
			"max_attempts":        quiz.MaxAttempts,
			"show_results":        quiz.ShowResults,
			"show_correct_answer": quiz.ShowCorrectAnswer,
			"allow_review":        quiz.AllowReview,
		},
	})
}

// @Summary Get quiz attempt detail
// @Description Get detailed information about a specific quiz attempt
// @Tags quiz
// @Produce json
// @Security BearerAuth
// @Param attempt_id path string true "Quiz Attempt ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 401 {object} map[string]string
// @Failure 403 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/attempts/{attempt_id}/detail [get]
func GetQuizAttemptDetail(c *gin.Context) {
	attemptID, err := strconv.ParseUint(c.Param("attempt_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid attempt ID"})
		return
	}

	userID, _ := c.Get("user_id")
	userRole, _ := c.Get("user_role")

	var attempt models.QuizAttempt
	query := connection.DB.Preload("Quiz").Preload("Answers")

	// Admin can view any attempt, users can only view their own
	if userRole == models.RoleAdmin {
		query = query.Preload("User")
	} else {
		query = query.Where("user_id = ?", userID.(uint))
	}

	if err := query.First(&attempt, uint(attemptID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz attempt not found"})
		return
	}

	// Check if user can view results
	if userRole != models.RoleAdmin && !attempt.Quiz.ShowResults {
		c.JSON(http.StatusForbidden, gin.H{"error": "Results not available for this quiz"})
		return
	}

	// Get attempt questions with bank questions and options
	var attemptQuestions []models.QuizAttemptQuestion
	if err := connection.DB.Where("quiz_attempt_id = ?", attempt.ID).
		Preload("BankQuestion.Options").
		Order("order_index").
		Find(&attemptQuestions).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch attempt questions"})
		return
	}

	// Get user answers for this attempt
	var userAnswers []models.Answer
	if err := connection.DB.Where("quiz_attempt_id = ?", attempt.ID).
		Find(&userAnswers).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch user answers"})
		return
	}

	// Group answers by bank_question_id for easier lookup
	answersByQuestion := make(map[uint][]uint)
	for _, answer := range userAnswers {
		answersByQuestion[answer.BankQuestionID] = append(answersByQuestion[answer.BankQuestionID], answer.BankOptionID)
	}

	// Build detailed response with questions, options, and user answers
	var detailedQuestions []gin.H
	for _, aq := range attemptQuestions {
		questionData := gin.H{
			"id":            aq.ID,
			"bank_question": aq.BankQuestion,
			"points":        aq.Points,
			"order_index":   aq.OrderIndex,
			"user_answers":  answersByQuestion[aq.BankQuestionID],
		}

		// Only show correct answers if allowed
		if attempt.Quiz.ShowCorrectAnswer || userRole == models.RoleAdmin {
			questionData["show_correct_answers"] = true
		} else {
			// Hide correct answer information
			for i := range aq.BankQuestion.Options {
				aq.BankQuestion.Options[i].IsCorrect = false
			}
			questionData["show_correct_answers"] = false
		}

		detailedQuestions = append(detailedQuestions, questionData)
	}

	response := gin.H{
		"attempt":   attempt,
		"questions": detailedQuestions,
	}

	c.JSON(http.StatusOK, response)
}
