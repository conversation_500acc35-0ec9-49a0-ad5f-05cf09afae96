package handlers

import (
	"net/http"
	"strconv"

	"etuto-database/connection"
	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

// @Summary Get quiz attempt stats
// @Description Get statistics for a quiz
// @Tags quiz
// @Produce json
// @Param id path string true "Quiz ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/{id}/stats [get]
func GetQuizAttemptStats(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	// Get quiz info
	var quiz models.Quiz
	if err := connection.DB.First(&quiz, uint(quizID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Get total attempts
	var totalAttempts int64
	connection.DB.Model(&models.QuizAttempt{}).
		Where("quiz_id = ?", uint(quizID)).
		Count(&totalAttempts)

	// Get passed attempts
	var passedAttempts int64
	connection.DB.Model(&models.QuizAttempt{}).
		Where("quiz_id = ? AND is_passed = ?", uint(quizID), true).
		Count(&passedAttempts)

	// Get unique users who attempted
	var uniqueUsers int64
	connection.DB.Model(&models.QuizAttempt{}).
		Where("quiz_id = ?", uint(quizID)).
		Distinct("user_id").
		Count(&uniqueUsers)

	// Calculate average score
	var avgScore float64
	connection.DB.Model(&models.QuizAttempt{}).
		Where("quiz_id = ?", uint(quizID)).
		Select("AVG(percentage)").
		Scan(&avgScore)

	c.JSON(http.StatusOK, gin.H{
		"quiz": quiz,
		"stats": gin.H{
			"total_attempts":  totalAttempts,
			"passed_attempts": passedAttempts,
			"failed_attempts": totalAttempts - passedAttempts,
			"unique_users":    uniqueUsers,
			"pass_rate": func() float64 {
				if totalAttempts > 0 {
					return float64(passedAttempts) / float64(totalAttempts) * 100
				}
				return 0
			}(),
			"average_score": avgScore,
		},
	})
}

// @Summary Get quiz attempts list
// @Description Get paginated list of quiz attempts for admin
// @Tags quiz
// @Produce json
// @Param id path string true "Quiz ID"
// @Param status query string false "Status filter (passed/failed)"
// @Param page query int false "Page number"
// @Param limit query int false "Number of items per page"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/quizzes/{id}/attempts [get]
func GetQuizAttemptsList(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	// Pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status") // "passed", "failed", or empty for all

	offset := (page - 1) * limit

	// Build query
	query := connection.DB.Model(&models.QuizAttempt{}).
		Where("quiz_id = ?", uint(quizID)).
		Preload("User").
		Preload("Quiz")

	// Filter by status if specified
	if status == "passed" {
		query = query.Where("is_passed = ?", true)
	} else if status == "failed" {
		query = query.Where("is_passed = ?", false)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Get attempts with pagination
	var attempts []models.QuizAttempt
	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&attempts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quiz attempts"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"attempts": attempts,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
			"pages": (total + int64(limit) - 1) / int64(limit),
		},
	})
}

// @Summary Get user quiz summary
// @Description Get quiz attempt summary for a specific user
// @Tags quiz
// @Produce json
// @Param user_id path string true "User ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/admin/users/{user_id}/quiz-summary [get]
func GetUserQuizSummary(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("user_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	// Get user info
	var user models.User
	if err := connection.DB.First(&user, uint(userID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Get user's quiz attempts with quiz info
	var attempts []models.QuizAttempt
	if err := connection.DB.Where("user_id = ?", uint(userID)).
		Preload("Quiz").
		Order("created_at DESC").
		Find(&attempts).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch user attempts"})
		return
	}

	// Calculate summary stats
	totalAttempts := len(attempts)
	passedAttempts := 0
	var totalScore float64

	for _, attempt := range attempts {
		if attempt.IsPassed {
			passedAttempts++
		}
		totalScore += attempt.Percentage
	}

	avgScore := float64(0)
	if totalAttempts > 0 {
		avgScore = totalScore / float64(totalAttempts)
	}

	c.JSON(http.StatusOK, gin.H{
		"user":     user,
		"attempts": attempts,
		"summary": gin.H{
			"total_attempts":  totalAttempts,
			"passed_attempts": passedAttempts,
			"failed_attempts": totalAttempts - passedAttempts,
			"pass_rate": func() float64 {
				if totalAttempts > 0 {
					return float64(passedAttempts) / float64(totalAttempts) * 100
				}
				return 0
			}(),
			"average_score": avgScore,
		},
	})
}

// @Summary Get all quizzes summary
// @Description Get summary of all quizzes for admin dashboard
// @Tags quiz
// @Produce json
// @Success 200 {object} interface{}
// @Failure 500 {object} map[string]string
// @Router /api/quizzes/summary [get]
func GetAllQuizzesSummary(c *gin.Context) {
	// Get all quizzes with attempt counts
	var quizzes []models.Quiz
	if err := connection.DB.Find(&quizzes).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quizzes"})
		return
	}

	type QuizSummary struct {
		Quiz           models.Quiz `json:"quiz"`
		TotalAttempts  int64       `json:"total_attempts"`
		PassedAttempts int64       `json:"passed_attempts"`
		UniqueUsers    int64       `json:"unique_users"`
		AverageScore   float64     `json:"average_score"`
		PassRate       float64     `json:"pass_rate"`
	}

	var summaries []QuizSummary

	for _, quiz := range quizzes {
		var totalAttempts, passedAttempts, uniqueUsers int64
		var avgScore float64

		// Get stats for this quiz
		connection.DB.Model(&models.QuizAttempt{}).
			Where("quiz_id = ?", quiz.ID).
			Count(&totalAttempts)

		connection.DB.Model(&models.QuizAttempt{}).
			Where("quiz_id = ? AND is_passed = ?", quiz.ID, true).
			Count(&passedAttempts)

		connection.DB.Model(&models.QuizAttempt{}).
			Where("quiz_id = ?", quiz.ID).
			Distinct("user_id").
			Count(&uniqueUsers)

		connection.DB.Model(&models.QuizAttempt{}).
			Where("quiz_id = ?", quiz.ID).
			Select("AVG(percentage)").
			Scan(&avgScore)

		passRate := float64(0)
		if totalAttempts > 0 {
			passRate = float64(passedAttempts) / float64(totalAttempts) * 100
		}

		summaries = append(summaries, QuizSummary{
			Quiz:           quiz,
			TotalAttempts:  totalAttempts,
			PassedAttempts: passedAttempts,
			UniqueUsers:    uniqueUsers,
			AverageScore:   avgScore,
			PassRate:       passRate,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"quizzes": summaries,
	})
}
