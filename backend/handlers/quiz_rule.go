package handlers

import (
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"etuto-database/connection"
	"etuto-database/models"

	"github.com/gin-gonic/gin"
)

// Quiz Rule handlers

type CreateQuizRuleRequest struct {
	CategoryID   uint                   `json:"category_id" binding:"required"`
	Difficulty   models.DifficultyLevel `json:"difficulty" binding:"required"`
	QuestionType models.QuestionType    `json:"question_type" binding:"required"`
	Percentage   float64                `json:"percentage" binding:"required,min=0.1,max=100"`
}

type UpdateQuizRuleRequest struct {
	CategoryID   uint                   `json:"category_id"`
	Difficulty   models.DifficultyLevel `json:"difficulty"`
	QuestionType models.QuestionType    `json:"question_type"`
	Percentage   float64                `json:"percentage" binding:"min=0.1,max=100"`
	OrderIndex   int                    `json:"order_index"`
}

type ReorderQuizRulesRequest struct {
	RuleOrders []struct {
		ID         uint `json:"id" binding:"required"`
		OrderIndex int  `json:"order_index" binding:"required"`
	} `json:"rule_orders" binding:"required"`
}

// @Summary Get quiz rules
// @Description Get a list of quiz rules for a specific quiz
// @Tags quiz
// @Produce json
// @Param quiz_id path string true "Quiz ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/quiz/{quiz_id}/rules [get]
func GetQuizRules(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var rules []models.QuizRule
	if err := connection.DB.Where("quiz_id = ?", uint(quizID)).
		Preload("Category").
		Order("order_index").
		Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quiz rules"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"rules": rules})
}

// @Summary Get quiz rule
// @Description Get a quiz rule by ID
// @Tags quiz
// @Produce json
// @Param id path string true "Rule ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/quiz-rules/{id} [get]
func GetQuizRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}

	var rule models.QuizRule
	if err := connection.DB.Preload("Category").First(&rule, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Rule not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"rule": rule})
}

// @Summary Create quiz rule
// @Description Create a new quiz rule
// @Tags quiz
// @Accept json
// @Produce json
// @Param quiz_id path string true "Quiz ID"
// @Param rule body handlers.CreateQuizRuleRequest true "Rule details"
// @Success 201 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/quiz/{quiz_id}/rules [post]
func CreateQuizRule(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var req CreateQuizRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if quiz exists
	var quiz models.Quiz
	if err := connection.DB.First(&quiz, uint(quizID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Check if category exists
	var category models.QuestionBankCategory
	if err := connection.DB.First(&category, req.CategoryID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Category not found"})
		return
	}

	// Validate that there are questions matching the criteria
	var questionCount int64
	connection.DB.Model(&models.BankQuestion{}).
		Where("category_id = ? AND difficulty = ? AND question_type = ?",
			req.CategoryID, req.Difficulty, req.QuestionType).
		Count(&questionCount)

	if questionCount == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No questions found matching the specified criteria"})
		return
	}

	// Use the quiz already loaded above to calculate count from percentage

	// Calculate count from percentage for validation
	calculatedCount := int(float64(quiz.TotalQuestions) * req.Percentage / 100.0)
	if calculatedCount < 1 {
		calculatedCount = 1 // Minimum 1 question
	}

	if calculatedCount > int(questionCount) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":      "Calculated count from percentage exceeds available questions",
			"available":  questionCount,
			"calculated": calculatedCount,
			"percentage": req.Percentage,
		})
		return
	}

	// Get the next order index
	var maxOrderIndex int
	connection.DB.Model(&models.QuizRule{}).
		Where("quiz_id = ?", uint(quizID)).
		Select("COALESCE(MAX(order_index), 0)").
		Scan(&maxOrderIndex)

	rule := models.QuizRule{
		QuizID:       uint(quizID),
		CategoryID:   req.CategoryID,
		Difficulty:   req.Difficulty,
		QuestionType: req.QuestionType,
		Percentage:   req.Percentage,
		OrderIndex:   maxOrderIndex + 1,
	}

	if err := connection.DB.Create(&rule).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quiz rule"})
		return
	}

	// Load relationships
	connection.DB.Preload("Category").First(&rule, rule.ID)

	c.JSON(http.StatusCreated, gin.H{"rule": rule})
}

// @Summary Update quiz rule
// @Description Update an existing quiz rule
// @Tags quiz
// @Accept json
// @Produce json
// @Param id path string true "Rule ID"
// @Param rule body handlers.UpdateQuizRuleRequest true "Rule details"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Router /api/question-bank/quiz/{quiz_id}/rules/{id} [put]
func UpdateQuizRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}

	var req UpdateQuizRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var rule models.QuizRule
	if err := connection.DB.First(&rule, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Rule not found"})
		return
	}

	// Update fields
	if req.CategoryID != 0 {
		rule.CategoryID = req.CategoryID
	}
	if req.Difficulty != "" {
		rule.Difficulty = req.Difficulty
	}
	if req.QuestionType != "" {
		rule.QuestionType = req.QuestionType
	}
	if req.Percentage > 0 {
		rule.Percentage = req.Percentage
	}
	if req.OrderIndex != 0 {
		rule.OrderIndex = req.OrderIndex
	}

	// Validate updated criteria
	if req.CategoryID != 0 || req.Difficulty != "" || req.QuestionType != "" || req.Percentage > 0 {
		var questionCount int64
		connection.DB.Model(&models.BankQuestion{}).
			Where("category_id = ? AND difficulty = ? AND question_type = ?",
				rule.CategoryID, rule.Difficulty, rule.QuestionType).
			Count(&questionCount)

		if questionCount == 0 {
			c.JSON(http.StatusBadRequest, gin.H{"error": "No questions found matching the updated criteria"})
			return
		}

		// Get quiz to calculate count for validation
		var quiz models.Quiz
		if err := connection.DB.First(&quiz, rule.QuizID).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
			return
		}

		calculatedCount := rule.GetCalculatedCount(quiz.TotalQuestions)
		if calculatedCount > int(questionCount) {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":      "Calculated count from percentage exceeds available questions",
				"available":  questionCount,
				"calculated": calculatedCount,
				"percentage": rule.Percentage,
			})
			return
		}
	}

	if err := connection.DB.Save(&rule).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update quiz rule"})
		return
	}

	// Load relationships
	connection.DB.Preload("Category").First(&rule, rule.ID)

	c.JSON(http.StatusOK, gin.H{"rule": rule})
}

// @Summary Delete quiz rule
// @Description Delete a quiz rule
// @Tags quiz
// @Produce json
// @Param id path string true "Rule ID"
// @Success 200 {object} map[string]string
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/question-bank/quiz/{quiz_id}/rules/{id} [delete]
func DeleteQuizRule(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid rule ID"})
		return
	}

	if err := connection.DB.Delete(&models.QuizRule{}, uint(id)).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete quiz rule"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quiz rule deleted successfully"})
}

// @Summary Reorder quiz rules
// @Description Reorder quiz rules
// @Tags quiz
// @Accept json
// @Produce json
// @Param quiz_id path string true "Quiz ID"
// @Param order body handlers.ReorderQuizRulesRequest true "New order"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/question-bank/quiz/{quiz_id}/rules/reorder [put]
func ReorderQuizRules(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var req ReorderQuizRulesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if quiz exists
	var quiz models.Quiz
	if err := connection.DB.First(&quiz, uint(quizID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Quiz not found"})
		return
	}

	// Update order for each rule
	tx := connection.DB.Begin()
	for _, ruleOrder := range req.RuleOrders {
		if err := tx.Model(&models.QuizRule{}).
			Where("id = ? AND quiz_id = ?", ruleOrder.ID, uint(quizID)).
			Update("order_index", ruleOrder.OrderIndex).Error; err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update rule order"})
			return
		}
	}
	tx.Commit()

	// Return updated rules
	var rules []models.QuizRule
	if err := connection.DB.Where("quiz_id = ?", uint(quizID)).
		Preload("Category").
		Order("order_index").
		Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch updated rules"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"rules": rules})
}

// @Summary Preview quiz questions
// @Description Preview quiz questions based on quiz rules
// @Tags quiz
// @Produce json
// @Param quiz_id path string true "Quiz ID"
// @Success 200 {object} interface{}
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /api/question-bank/quiz/{quiz_id}/preview [get]
func PreviewQuizQuestions(c *gin.Context) {
	quizID, err := strconv.ParseUint(c.Param("quiz_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quiz ID"})
		return
	}

	var rules []models.QuizRule
	if err := connection.DB.Where("quiz_id = ?", uint(quizID)).
		Preload("Category").
		Order("order_index").
		Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch quiz rules"})
		return
	}

	type PreviewResult struct {
		Rule               models.QuizRule       `json:"rule"`
		AvailableQuestions int64                 `json:"available_questions"`
		SampleQuestions    []models.BankQuestion `json:"sample_questions"`
	}

	var preview []PreviewResult

	for _, rule := range rules {
		var questionCount int64
		connection.DB.Model(&models.BankQuestion{}).
			Where("category_id = ? AND difficulty = ? AND question_type = ?",
				rule.CategoryID, rule.Difficulty, rule.QuestionType).
			Count(&questionCount)

		var sampleQuestions []models.BankQuestion
		connection.DB.Where("category_id = ? AND difficulty = ? AND question_type = ?",
			rule.CategoryID, rule.Difficulty, rule.QuestionType).
			Preload("Options").
			Limit(3). // Show max 3 sample questions
			Find(&sampleQuestions)

		// Shuffle options for each sample question
		for i := range sampleQuestions {
			if sampleQuestions[i].Options != nil {
				sampleQuestions[i].Options = shuffleBankOptions(sampleQuestions[i].Options)
			}
		}

		preview = append(preview, PreviewResult{
			Rule:               rule,
			AvailableQuestions: questionCount,
			SampleQuestions:    sampleQuestions,
		})
	}

	c.JSON(http.StatusOK, gin.H{"preview": preview})
}

// Helper function to shuffle bank options for a question
func shuffleBankOptions(options []models.BankOption) []models.BankOption {
	if len(options) <= 1 {
		return options
	}

	// Create a copy to avoid modifying the original slice
	shuffled := make([]models.BankOption, len(options))
	copy(shuffled, options)

	// Shuffle using Fisher-Yates algorithm
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := len(shuffled) - 1; i > 0; i-- {
		j := r.Intn(i + 1)
		shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
	}

	return shuffled
}
