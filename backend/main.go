package main

import (
	"log"
	"os"

	"etuto-backend/middleware"
	"etuto-backend/routes"
	"etuto-database/connection"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
)

// @title           <PERSON><PERSON> thống <PERSON>ọ<PERSON> và <PERSON>hi trực tuyến eTuto
// @version         1.0
// @description     API cho Hệ thống eTuto

// @contact.name   API Support
// @contact.email  <EMAIL>

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Xác thực bằng JWT Token với tiền tố Bearer. Ví dụ: "Bearer {token}"
func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize database
	if err := connection.Connect(); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Initialize Gin router
	r := gin.Default()
	r.Use(middleware.CORSMiddleware())

	// Setup routes
	routes.SetupRoutes(r)

	// Get port from environment or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	log.Fatal(r.Run(":" + port))
}
