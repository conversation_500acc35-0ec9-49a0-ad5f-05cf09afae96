package main

import (
	"fmt"
	"log"
	"os"

	"etuto-database/connection"
	"etuto-database/migrations"
	"etuto-database/seeds"

	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	command := os.Args[1]

	switch command {
	case "migrate":
		runMigrations()
	case "seed":
		runSeeds()
	case "reset":
		runReset()
	case "setup":
		runSetup()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Println("Usage: go run database/main.go <command>")
	fmt.Println("")
	fmt.Println("Commands:")
	fmt.Println("  migrate  - Run database migrations")
	fmt.Println("  seed     - Run database seeds")
	fmt.Println("  reset    - Drop all tables and recreate (WARNING: This will delete all data)")
	fmt.Println("  setup    - Run migrations and seeds (equivalent to migrate + seed)")
}

func runMigrations() {
	log.Println("Connecting to database...")
	if err := connection.Connect(); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	log.Println("Running migrations...")
	if err := migrations.Migrate(); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	log.Println("Migrations completed successfully!")
}

func runSeeds() {
	log.Println("Running seeds...")
	if err := seeds.RunSeeds(); err != nil {
		log.Fatal("Failed to run seeds:", err)
	}

	log.Println("Seeds completed successfully!")
}

func runReset() {
	log.Println("WARNING: This will delete all data in the database!")
	fmt.Print("Are you sure you want to continue? (y/N): ")

	var response string
	fmt.Scanln(&response)

	if response != "y" && response != "Y" {
		log.Println("Operation cancelled.")
		return
	}

	log.Println("Connecting to database...")
	if err := connection.Connect(); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	db := connection.GetDB()

	// Drop all tables
	log.Println("Dropping all tables...")
	if err := db.Migrator().DropTable(
		// Question Bank tables (drop first due to foreign keys)
		"quiz_attempt_questions",
		"quiz_rules",
		"bank_options",
		"bank_questions",
		"question_bank_categories",
		// Original tables
		"answers",
		"quiz_attempts",
		"options",
		"questions",
		"quizzes",
		"categories",
		"user_progresses",
		"slides",
		"lessons",
		"enrollments",
		"courses",
		"users",
	); err != nil {
		log.Printf("Warning: Some tables might not exist: %v", err)
	}

	log.Println("Running migrations...")
	if err := migrations.Migrate(); err != nil {
		log.Fatal("Failed to run migrations:", err)
	}

	log.Println("Database reset completed successfully!")
}

func runSetup() {
	runMigrations()
	runSeeds()
	log.Println("Database setup completed successfully!")
}
