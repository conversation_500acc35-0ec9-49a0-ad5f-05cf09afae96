package migrations

import (
	"log"

	"etuto-database/connection"
	"etuto-database/models"
)

func Migrate() error {
	log.Println("Running database migrations...")

	err := connection.DB.AutoMigrate(
		&models.User{},
		&models.Course{},
		&models.Lesson{},
		&models.Slide{},
		&models.Enrollment{},
		&models.UserProgress{},
		&models.Category{},
		&models.Quiz{},
		&models.QuizAttempt{},
		&models.Answer{},
		// Question Bank models
		&models.QuestionBankCategory{},
		&models.BankQuestion{},
		&models.BankOption{},
		&models.QuizRule{},
		&models.QuizAttemptQuestion{},
	)

	if err != nil {
		return err
	}

	log.Println("Database migration completed successfully")

	// Create default admin user if not exists
	return createDefaultAdmin()
}

func createDefaultAdmin() error {
	var count int64
	connection.DB.Model(&models.User{}).Where("role = ?", models.RoleAdmin).Count(&count)

	if count == 0 {
		log.Println("Creating default admin user...")

		// Note: In production, hash the password properly
		admin := models.User{
			Email:     "<EMAIL>",
			Password:  "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
			FirstName: "Admin",
			LastName:  "User",
			Role:      models.RoleAdmin,
			IsActive:  true,
		}

		if err := connection.DB.Create(&admin).Error; err != nil {
			log.Printf("Failed to create default admin: %v", err)
			return err
		} else {
			log.Println("Default admin created: <EMAIL> / password")
		}
	}

	return nil
}
