question_id,option_text,is_correct,order_index
css_1,"color",true,1
css_1,"text-color",false,2
css_1,"font-color",false,3
css_1,"background-color",false,4
css_2,"background-color",true,1
css_2,"bg-color",false,2
css_2,"color",false,3
css_2,"background",false,4
css_3,"<link rel=""stylesheet"" href=""style.css"">",true,1
css_3,"<css>style.css</css>",false,2
css_3,"<style src=""style.css"">",false,3
css_3,"<link src=""style.css"">",false,4
css_4,"font-size",true,1
css_4,"text-size",false,2
css_4,"font-style",false,3
css_4,"text-style",false,4
css_5,"margin",true,1
css_5,"padding",true,2
css_5,"border",false,3
css_5,"spacing",false,4
css_6,"Cascading Style Sheets",true,1
css_6,"Computer Style Sheets",false,2
css_6,"Creative Style Sheets",false,3
css_6,"Colorful Style Sheets",false,4
css_7,"font-weight",true,1
css_7,"text-weight",false,2
css_7,"font-style",false,3
css_7,"text-decoration",false,4
css_8,"text-align",true,1
css_8,"align",false,2
css_8,"text-center",false,3
css_8,"center",false,4
css_9,".classname",true,1
css_9,"#classname",false,2
css_9,"classname",false,3
css_9,"*classname",false,4
css_10,"#idname",true,1
css_10,".idname",false,2
css_10,"idname",false,3
css_10,"*idname",false,4
css_11,"block",true,1
css_11,"inline",false,2
css_11,"flex",true,3
css_11,"grid",true,4
css_12,"Content, padding, border, and margin",true,1
css_12,"Width, height, and color",false,2
css_12,"Font, size, and weight",false,3
css_12,"Top, right, bottom, and left",false,4
css_13,"em",true,1
css_13,"px",false,2
css_13,"rem",true,3
css_13,"%",true,4
css_14,"Margin is outside the border, padding is inside",true,1
css_14,"Padding is outside the border, margin is inside",false,2
css_14,"They are exactly the same",false,3
css_14,"Margin is for text, padding is for images",false,4
css_15,"justify-content",true,1
css_15,"align-items",true,2
css_15,"text-align",false,3
css_15,"flex-direction",true,4
css_16,"To apply different styles based on device characteristics",true,1
css_16,"To load external CSS files",false,2
css_16,"To validate CSS syntax",false,3
css_16,"To compress CSS files",false,4
css_17,":hover",true,1
css_17,":focus",true,2
css_17,":click",false,3
css_17,":active",true,4
css_18,"The process of combining multiple stylesheets and resolving conflicts",true,1
css_18,"A CSS animation effect",false,2
css_18,"A CSS layout technique",false,3
css_18,"A CSS validation tool",false,4
css_19,"position",true,1
css_19,"top",true,2
css_19,"display",false,3
css_19,"z-index",true,4
css_20,"Block elements take full width, inline elements only take necessary width",true,1
css_20,"Inline elements take full width, block elements only take necessary width",false,2
css_20,"They are exactly the same",false,3
css_20,"Block elements are for text, inline elements are for images",false,4
css_21,"A system that determines which CSS rules apply when multiple rules target the same element",true,1
css_21,"The speed at which CSS loads",false,2
css_21,"The number of CSS files in a project",false,3
css_21,"A CSS validation tool",false,4
css_22,"grid-template-columns",true,1
css_22,"grid-template-rows",true,2
css_22,"grid-gap",false,3
css_22,"grid-template-areas",true,4
css_23,"To optimize rendering performance by isolating subtrees",true,1
css_23,"To prevent CSS from loading",false,2
css_23,"To contain text within elements",false,3
css_23,"To create CSS animations",false,4
css_24,"Inheritance",true,1
css_24,"Fallback values",true,2
css_24,"Static typing",false,3
css_24,"Runtime modification",true,4
css_25,"Using start/end instead of left/right for better internationalization",true,1
css_25,"Using mathematical logic in CSS",false,2
css_25,"A CSS debugging technique",false,3
css_25,"A CSS optimization method",false,4
css_26,"will-change",true,1
css_26,"transform",true,2
css_26,"color",false,3
css_26,"contain",true,4
css_27,"A set of APIs that expose parts of the CSS engine to developers",true,1
css_27,"A CSS framework",false,2
css_27,"A CSS preprocessor",false,3
css_27,"A CSS validation tool",false,4
css_28,"@supports",true,1
css_28,"@layer",true,2
css_28,"@color",false,3
css_28,"@container",true,4
css_29,"A way for grid items to participate in their parent's grid",true,1
css_29,"A smaller version of CSS Grid",false,2
css_29,"A CSS animation technique",false,3
css_29,"A CSS debugging tool",false,4
css_30,"@layer rule",true,1
css_30,"Layer ordering",true,2
css_30,"Automatic layering",false,3
css_30,"Unlayered styles",true,4
