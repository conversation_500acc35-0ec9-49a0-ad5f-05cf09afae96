question_id,text,type,points,difficulty,tags
css_1,"Which CSS property is used to change text color?",single_choice,1,easy,"css,color,text"
css_2,"Which CSS property is used to change the background color?",single_choice,1,easy,"css,background,color"
css_3,"How do you add a CSS file to an HTML document?",single_choice,1,easy,"css,linking,html"
css_4,"Which CSS property controls the text size?",single_choice,1,easy,"css,font,size"
css_5,"Which CSS properties are used for spacing?",multiple_choice,1,easy,"css,spacing,layout"
css_6,"What does CSS stand for?",single_choice,1,easy,"css,basics,acronym"
css_7,"Which CSS property is used to make text bold?",single_choice,1,easy,"css,font,weight"
css_8,"Which CSS property is used to center text?",single_choice,1,easy,"css,text,alignment"
css_9,"Which CSS selector targets elements by class?",single_choice,1,easy,"css,selectors,class"
css_10,"Which CSS selector targets elements by ID?",single_choice,1,easy,"css,selectors,id"
css_11,"Which CSS display values create block-level elements?",multiple_choice,2,normal,"css,display,layout"
css_12,"What is the CSS box model?",single_choice,2,normal,"css,box-model,layout"
css_13,"Which CSS units are relative?",multiple_choice,2,normal,"css,units,responsive"
css_14,"What is the difference between margin and padding?",single_choice,2,normal,"css,spacing,box-model"
css_15,"Which CSS properties are used for flexbox?",multiple_choice,2,normal,"css,flexbox,layout"
css_16,"What is the purpose of CSS media queries?",single_choice,2,normal,"css,responsive,media-queries"
css_17,"Which CSS pseudo-classes are commonly used?",multiple_choice,2,normal,"css,pseudo-classes,selectors"
css_18,"What is the CSS cascade?",single_choice,2,normal,"css,cascade,inheritance"
css_19,"Which CSS properties control element positioning?",multiple_choice,2,normal,"css,positioning,layout"
css_20,"What is the difference between inline and block elements?",single_choice,2,normal,"css,display,elements"
css_21,"What is CSS specificity?",single_choice,3,hard,"css,specificity,selectors"
css_22,"Which CSS Grid properties define the grid structure?",multiple_choice,3,hard,"css,grid,layout"
css_23,"What is the CSS containment property used for?",single_choice,3,hard,"css,containment,performance"
css_24,"Which CSS custom properties features are advanced?",multiple_choice,3,hard,"css,custom-properties,variables"
css_25,"What is the CSS logical properties approach?",single_choice,3,hard,"css,logical-properties,internationalization"
css_26,"Which CSS features improve performance?",multiple_choice,3,hard,"css,performance,optimization"
css_27,"What is the CSS Houdini project?",single_choice,3,hard,"css,houdini,apis"
css_28,"Which CSS at-rules are used for advanced features?",multiple_choice,3,hard,"css,at-rules,advanced"
css_29,"What is CSS subgrid?",single_choice,3,hard,"css,subgrid,layout"
css_30,"Which CSS cascade layers features provide better control?",multiple_choice,3,hard,"css,cascade-layers,architecture"
