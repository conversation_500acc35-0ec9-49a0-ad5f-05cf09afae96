question_id,option_text,is_correct,order_index
db_1,"Structured Query Language",true,1
db_1,"Simple Query Language",false,2
db_1,"Standard Query Language",false,3
db_1,"System Query Language",false,4
db_2,"SELECT",true,1
db_2,"GET",false,2
db_2,"RETRIEVE",false,3
db_2,"FETCH",false,4
db_3,"A unique identifier for each record in a table",true,1
db_3,"A password for database access",false,2
db_3,"The first column in a table",false,3
db_3,"A backup key",false,4
db_4,"INSERT",true,1
db_4,"UPDATE",true,2
db_4,"CREATE",false,3
db_4,"DELETE",true,4
db_5,"A collection of related data organized in rows and columns",true,1
db_5,"A type of database software",false,2
db_5,"A database backup",false,3
db_5,"A user account",false,4
db_6,"WHERE",true,1
db_6,"FILTER",false,2
db_6,"HAVING",false,3
db_6,"LIMIT",false,4
db_7,"Create, Read, Update, Delete",true,1
db_7,"Connect, Retrieve, Update, Drop",false,2
db_7,"Copy, Read, Upload, Download",false,3
db_7,"Create, Restore, Update, Destroy",false,4
db_8,"VARCHAR",true,1
db_8,"INTEGER",true,2
db_8,"LOOP",false,3
db_8,"DATE",true,4
db_9,"The structure and organization of a database",true,1
db_9,"A database backup file",false,2
db_9,"A type of query",false,3
db_9,"A user permission",false,4
db_10,"CREATE TABLE",true,1
db_10,"NEW TABLE",false,2
db_10,"MAKE TABLE",false,3
db_10,"ADD TABLE",false,4
db_11,"A field that refers to the primary key of another table",true,1
db_11,"A unique identifier for a table",false,2
db_11,"An index on a table",false,3
db_11,"A constraint that prevents null values",false,4
db_12,"2NF",true,1
db_12,"1NF",false,2
db_12,"3NF",true,3
db_12,"BCNF",true,4
db_13,"To improve query performance by creating faster access paths",true,1
db_13,"To backup database data",false,2
db_13,"To encrypt sensitive data",false,3
db_13,"To compress database files",false,4
db_14,"INNER JOIN",true,1
db_14,"LEFT JOIN",true,2
db_14,"MIDDLE JOIN",false,3
db_14,"RIGHT JOIN",true,4
db_15,"The process of organizing data to reduce redundancy and improve integrity",true,1
db_15,"Making all data the same format",false,2
db_15,"Backing up the database",false,3
db_15,"Encrypting database data",false,4
db_16,"COUNT",true,1
db_16,"SUM",true,2
db_16,"CONCAT",false,3
db_16,"AVG",true,4
db_17,"DELETE can use WHERE clause, TRUNCATE removes all rows",true,1
db_17,"TRUNCATE can use WHERE clause, DELETE removes all rows",false,2
db_17,"They are exactly the same",false,3
db_17,"DELETE is faster than TRUNCATE",false,4
db_18,"NOT NULL",true,1
db_18,"UNIQUE",true,2
db_18,"INDEX",false,3
db_18,"CHECK",true,4
db_19,"A virtual table based on the result of a SQL query",true,1
db_19,"A way to look at database files",false,2
db_19,"A backup of database data",false,3
db_19,"A user interface for databases",false,4
db_20,"Atomicity",true,1
db_20,"Consistency",true,2
db_20,"Availability",false,3
db_20,"Durability",true,4
db_21,"READ COMMITTED",true,1
db_21,"READ UNCOMMITTED",false,2
db_21,"REPEATABLE READ",true,3
db_21,"SERIALIZABLE",true,4
db_22,"Horizontal partitioning to distribute data across multiple servers",true,1
db_22,"Creating backup copies of data",false,2
db_22,"Encrypting sensitive data",false,3
db_22,"Compressing data to save space",false,4
db_23,"B-tree indexes",true,1
db_23,"Hash indexes",true,2
db_23,"Linear indexes",false,3
db_23,"Bitmap indexes",true,4
db_24,"You can only guarantee 2 of 3: Consistency, Availability, Partition tolerance",true,1
db_24,"All three properties can always be guaranteed",false,2
db_24,"Only one property can be guaranteed at a time",false,3
db_24,"CAP stands for Create, Alter, Partition",false,4
db_25,"Master-slave replication",true,1
db_25,"Master-master replication",true,2
db_25,"Single-point replication",false,3
db_25,"Asynchronous replication",true,4
db_26,"To reuse database connections and improve performance",true,1
db_26,"To backup database connections",false,2
db_26,"To encrypt database connections",false,3
db_26,"To monitor database usage",false,4
db_27,"Document stores",true,1
db_27,"Key-value stores",true,2
db_27,"Relational stores",false,3
db_27,"Graph databases",true,4
db_28,"The system will become consistent over time if no new updates are made",true,1
db_28,"The system is always consistent",false,2
db_28,"The system is never consistent",false,3
db_28,"Consistency is checked every hour",false,4
db_29,"Query plan optimization",true,1
db_29,"Materialized views",true,2
db_29,"Random access",false,3
db_29,"Partitioning",true,4
db_30,"To automatically execute code in response to database events",true,1
db_30,"To manually start database operations",false,2
db_30,"To schedule database backups",false,3
db_30,"To create database connections",false,4
