question_id,text,type,points,difficulty,tags
db_1,"What does SQL stand for?",single_choice,1,easy,"sql,database,acronym"
db_2,"Which SQL command is used to retrieve data?",single_choice,1,easy,"sql,select,query"
db_3,"What is a primary key?",single_choice,1,easy,"database,primary-key,basics"
db_4,"Which SQL commands are used for data manipulation?",multiple_choice,1,easy,"sql,dml,commands"
db_5,"What is a database table?",single_choice,1,easy,"database,table,basics"
db_6,"Which SQL clause is used to filter results?",single_choice,1,easy,"sql,where,filtering"
db_7,"What does CRUD stand for in database operations?",single_choice,1,easy,"database,crud,operations"
db_8,"Which data types are commonly used in databases?",multiple_choice,1,easy,"database,data-types,basics"
db_9,"What is a database schema?",single_choice,1,easy,"database,schema,structure"
db_10,"Which SQL command is used to create a new table?",single_choice,1,easy,"sql,ddl,create"
db_11,"What is a foreign key?",single_choice,2,normal,"database,keys,relationships"
db_12,"Which normal forms eliminate partial dependencies?",multiple_choice,2,normal,"database,normalization,normal-forms"
db_13,"What is the purpose of database indexing?",single_choice,2,normal,"database,indexing,performance"
db_14,"Which SQL JOIN types are commonly used?",multiple_choice,2,normal,"sql,joins,relationships"
db_15,"What is database normalization?",single_choice,2,normal,"database,normalization,design"
db_16,"Which SQL aggregate functions are commonly used?",multiple_choice,2,normal,"sql,aggregate,functions"
db_17,"What is the difference between DELETE and TRUNCATE?",single_choice,2,normal,"sql,delete,truncate"
db_18,"Which database constraints ensure data integrity?",multiple_choice,2,normal,"database,constraints,integrity"
db_19,"What is a database view?",single_choice,2,normal,"database,views,virtual-tables"
db_20,"Which are characteristics of ACID properties?",multiple_choice,2,normal,"database,acid,transactions"
db_21,"Which isolation levels prevent dirty reads?",multiple_choice,3,hard,"database,isolation,transactions"
db_22,"What is the purpose of database sharding?",single_choice,3,hard,"database,sharding,scalability"
db_23,"Which advanced indexing techniques improve performance?",multiple_choice,3,hard,"database,indexing,advanced"
db_24,"What is the CAP theorem in distributed databases?",single_choice,3,hard,"database,cap-theorem,distributed"
db_25,"Which database replication strategies are commonly used?",multiple_choice,3,hard,"database,replication,high-availability"
db_26,"What is the purpose of database connection pooling?",single_choice,3,hard,"database,connection-pooling,performance"
db_27,"Which NoSQL database types serve different use cases?",multiple_choice,3,hard,"database,nosql,types"
db_28,"What is eventual consistency in distributed systems?",single_choice,3,hard,"database,consistency,distributed"
db_29,"Which database optimization techniques improve query performance?",multiple_choice,3,hard,"database,optimization,performance"
db_30,"What is the purpose of database triggers?",single_choice,3,hard,"database,triggers,automation"
