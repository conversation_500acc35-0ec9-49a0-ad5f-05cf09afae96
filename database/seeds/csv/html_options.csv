question_id,option_text,is_correct,order_index
html_1,"HyperText Markup Language",true,1
html_1,"High Tech Modern Language",false,2
html_1,"Home Tool Markup Language",false,3
html_1,"Hyperlink and Text Markup Language",false,4
html_2,"<p>",true,1
html_2,"<paragraph>",false,2
html_2,"<para>",false,3
html_2,"<text>",false,4
html_3,"<h1>",true,1
html_3,"<h3>",true,2
html_3,"<header>",false,3
html_3,"<h6>",true,4
html_4,"<br>",true,1
html_4,"<break>",false,2
html_4,"<lb>",false,3
html_4,"<newline>",false,4
html_5,"<a>",true,1
html_5,"<link>",false,2
html_5,"<href>",false,3
html_5,"<url>",false,4
html_6,"<img>",true,1
html_6,"<image>",false,2
html_6,"<picture>",false,3
html_6,"<photo>",false,4
html_7,"<ul>",true,1
html_7,"<ol>",true,2
html_7,"<list>",false,3
html_7,"<li>",true,4
html_8,"The title of the document shown in browser tab",true,1
html_8,"A heading on the page",false,2
html_8,"The main content title",false,3
html_8,"A tooltip text",false,4
html_9,"<table>",true,1
html_9,"<tab>",false,2
html_9,"<grid>",false,3
html_9,"<data>",false,4
html_10,"<h1>",true,1
html_10,"<h6>",false,2
html_10,"<heading>",false,3
html_10,"<header>",false,4
html_11,"<header>",true,1
html_11,"<div>",false,2
html_11,"<section>",true,3
html_11,"<span>",false,4
html_12,"<link rel=""stylesheet"" href=""style.css"">",true,1
html_12,"<css src=""style.css"">",false,2
html_12,"<style src=""style.css"">",false,3
html_12,"<include css=""style.css"">",false,4
html_13,"email",true,1
html_13,"date",true,2
html_13,"phone",false,3
html_13,"number",true,4
html_14,"To tell the browser which version of HTML the page is using",true,1
html_14,"To include CSS styles",false,2
html_14,"To define the page title",false,3
html_14,"To create a comment",false,4
html_15,"src",true,1
html_15,"alt",true,2
html_15,"width",false,3
html_15,"height",false,4
html_16,"<div> is block-level, <span> is inline",true,1
html_16,"<div> is inline, <span> is block-level",false,2
html_16,"They are exactly the same",false,3
html_16,"<div> is for text, <span> is for images",false,4
html_17,"<article>",true,1
html_17,"<aside>",true,2
html_17,"<div>",false,3
html_17,"<nav>",true,4
html_18,"<form action=""submit.php"" method=""post"">",true,1
html_18,"<form href=""submit.php"">",false,2
html_18,"<input form=""submit.php"">",false,3
html_18,"<form src=""submit.php"">",false,4
html_19,"description",true,1
html_19,"keywords",true,2
html_19,"author",false,3
html_19,"viewport",false,4
html_20,"To contain metadata about the document",true,1
html_20,"To display the main content",false,2
html_20,"To create the page header",false,3
html_20,"To define navigation links",false,4
html_21,"aria-label",true,1
html_21,"role",true,2
html_21,"class",false,3
html_21,"alt",true,4
html_22,"Delays script execution until HTML parsing is complete",true,1
html_22,"Loads script asynchronously",false,2
html_22,"Prevents script from executing",false,3
html_22,"Caches the script for faster loading",false,4
html_23,"Geolocation API",true,1
html_23,"Local Storage API",true,2
html_23,"CSS API",false,3
html_23,"Canvas API",true,4
html_24,"async executes immediately when loaded, defer waits for HTML parsing",true,1
html_24,"defer executes immediately, async waits for HTML parsing",false,2
html_24,"They are exactly the same",false,3
html_24,"async is for CSS, defer is for JavaScript",false,4
html_25,"script-src",true,1
html_25,"style-src",true,2
html_25,"font-src",false,3
html_25,"default-src",true,4
html_26,"To load critical resources early in the page lifecycle",true,1
html_26,"To prevent resources from loading",false,2
html_26,"To load resources after page load",false,3
html_26,"To compress resources",false,4
html_27,"required",true,1
html_27,"pattern",true,2
html_27,"placeholder",false,3
html_27,"min/max",true,4
html_28,"To control how cross-origin requests are handled",true,1
html_28,"To prevent cross-origin requests",false,2
html_28,"To enable all cross-origin requests",false,3
html_28,"To compress cross-origin data",false,4
html_29,"div",true,1
html_29,"p",true,2
html_29,"img",false,3
html_29,"span",true,4
html_30,"preload is for current page resources, prefetch is for future navigation",true,1
html_30,"prefetch is for current page, preload is for future navigation",false,2
html_30,"They are exactly the same",false,3
html_30,"preload is faster than prefetch",false,4
