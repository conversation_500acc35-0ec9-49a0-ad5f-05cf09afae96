question_id,option_text,is_correct,order_index
js_1,"var name",true,1
js_1,"let name",true,2
js_1,"const name",true,3
js_1,"variable name",false,4
js_2,"console.log()",true,1
js_2,"print()",false,2
js_2,"write()",false,3
js_2,"output()",false,4
js_3,"function myFunction() {}",true,1
js_3,"create myFunction() {}",false,2
js_3,"def myFunction() {}",false,3
js_3,"function = myFunction() {}",false,4
js_4,"string",true,1
js_4,"number",true,2
js_4,"object",false,3
js_4,"boolean",true,4
js_5,"// This is a comment",true,1
js_5,"# This is a comment",false,2
js_5,"<!-- This is a comment -->",false,3
js_5,"* This is a comment *",false,4
js_6,"+",true,1
js_6,"&",false,2
js_6,"*",false,3
js_6,".",false,4
js_7,"var colors = ['red', 'green', 'blue']",true,1
js_7,"var colors = (1:'red', 2:'green', 3:'blue')",false,2
js_7,"var colors = 'red', 'green', 'blue'",false,3
js_7,"var colors = 1 = 'red', 2 = 'green', 3 = 'blue'",false,4
js_8,"push()",true,1
js_8,"add()",false,2
js_8,"append()",false,3
js_8,"insert()",false,4
js_9,"An intentional absence of any object value",true,1
js_9,"An undefined variable",false,2
js_9,"An empty string",false,3
js_9,"Zero value",false,4
js_10,"break",true,1
js_10,"stop",false,2
js_10,"exit",false,3
js_10,"end",false,4
js_11,"== compares values, === compares values and types",true,1
js_11,"They are exactly the same",false,2
js_11,"=== is faster than ==",false,3
js_11,"== is for numbers, === is for strings",false,4
js_12,"Promises",true,1
js_12,"async/await",true,2
js_12,"for loops",false,3
js_12,"callbacks",true,4
js_13,"Refers to the object that is executing the current function",true,1
js_13,"Creates a new variable",false,2
js_13,"Defines a constant",false,3
js_13,"Imports a module",false,4
js_14,"map()",true,1
js_14,"filter()",true,2
js_14,"push()",false,3
js_14,"slice()",true,4
js_15,"A function that has access to variables in its outer scope",true,1
js_15,"A way to close a program",false,2
js_15,"A type of loop",false,3
js_15,"A method to hide variables",false,4
js_16,"Template literals",true,1
js_16,"Arrow functions",true,2
js_16,"var keyword",false,3
js_16,"Destructuring",true,4
js_17,"let has block scope, var has function scope",true,1
js_17,"var has block scope, let has function scope",false,2
js_17,"They are exactly the same",false,3
js_17,"let is faster than var",false,4
js_18,"getElementById()",true,1
js_18,"querySelector()",true,2
js_18,"console.log()",false,3
js_18,"addEventListener()",true,4
js_19,"Events propagate from child to parent elements",true,1
js_19,"Events propagate from parent to child elements",false,2
js_19,"Events are cancelled automatically",false,3
js_19,"Events are duplicated",false,4
js_20,"Higher-order functions",true,1
js_20,"Pure functions",true,2
js_20,"Global variables",false,3
js_20,"Immutability",true,4
js_21,"A mechanism that handles asynchronous operations in single-threaded JavaScript",true,1
js_21,"A type of loop for iterating arrays",false,2
js_21,"A method for creating events",false,3
js_21,"A debugging tool",false,4
js_22,"Module pattern",true,1
js_22,"Observer pattern",true,2
js_22,"Global variables",false,3
js_22,"Prototype pattern",true,4
js_23,"call/apply invoke immediately, bind returns a new function",true,1
js_23,"They are exactly the same",false,2
js_23,"bind is faster than call and apply",false,3
js_23,"call is for objects, apply is for arrays",false,4
js_24,"Garbage collection",true,1
js_24,"Memory leaks",true,2
js_24,"Automatic allocation",false,3
js_24,"Reference counting",true,4
js_25,"To intercept and customize operations on objects",true,1
js_25,"To create network connections",false,2
js_25,"To handle errors",false,3
js_25,"To optimize performance",false,4
js_26,"JIT compilation",true,1
js_26,"Hidden classes",true,2
js_26,"Global variables",false,3
js_26,"Inline caching",true,4
js_27,"Microtasks have higher priority and execute before macrotasks",true,1
js_27,"Macrotasks have higher priority than microtasks",false,2
js_27,"They have the same priority",false,3
js_27,"They are executed in random order",false,4
js_28,"Reflect API",true,1
js_28,"Proxy objects",true,2
js_28,"console.log",false,3
js_28,"Symbol.iterator",true,4
js_29,"To hold weak references that don't prevent garbage collection",true,1
js_29,"To create faster data structures",false,2
js_29,"To store large amounts of data",false,3
js_29,"To create immutable collections",false,4
js_30,"Web Workers",true,1
js_30,"SharedArrayBuffer",true,2
js_30,"setTimeout",false,3
js_30,"Atomics",true,4
