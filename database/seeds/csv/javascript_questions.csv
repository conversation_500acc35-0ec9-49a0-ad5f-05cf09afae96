question_id,text,type,points,difficulty,tags
js_1,"How do you declare a variable in JavaScript?",multiple_choice,1,easy,"javascript,variables,declaration"
js_2,"Which method is used to write output in JavaScript?",single_choice,1,easy,"javascript,output,console"
js_3,"What is the correct way to create a function in JavaScript?",single_choice,1,easy,"javascript,functions,syntax"
js_4,"Which JavaScript data types are primitive?",multiple_choice,1,easy,"javascript,data-types,primitives"
js_5,"How do you create a comment in JavaScript?",single_choice,1,easy,"javascript,comments,syntax"
js_6,"Which operator is used for string concatenation?",single_choice,1,easy,"javascript,strings,operators"
js_7,"What is the correct way to write a JavaScript array?",single_choice,1,easy,"javascript,arrays,syntax"
js_8,"Which method is used to add an element to the end of an array?",single_choice,1,easy,"javascript,arrays,methods"
js_9,"What does 'null' represent in JavaScript?",single_choice,1,easy,"javascript,null,data-types"
js_10,"Which statement is used to stop a loop?",single_choice,1,easy,"javascript,loops,control"
js_11,"What is the difference between '==' and '===' in JavaScript?",single_choice,2,normal,"javascript,comparison,operators"
js_12,"Which JavaScript features support asynchronous programming?",multiple_choice,2,normal,"javascript,async,promises"
js_13,"What is the purpose of the 'this' keyword?",single_choice,2,normal,"javascript,this,context"
js_14,"Which array methods do not modify the original array?",multiple_choice,2,normal,"javascript,arrays,immutable"
js_15,"What is closure in JavaScript?",single_choice,2,normal,"javascript,closures,scope"
js_16,"Which ES6 features improve code readability?",multiple_choice,2,normal,"javascript,es6,features"
js_17,"What is the difference between 'let' and 'var'?",single_choice,2,normal,"javascript,variables,scope"
js_18,"Which methods are used for DOM manipulation?",multiple_choice,2,normal,"javascript,dom,manipulation"
js_19,"What is event bubbling?",single_choice,2,normal,"javascript,events,bubbling"
js_20,"Which JavaScript concepts relate to functional programming?",multiple_choice,2,normal,"javascript,functional,programming"
js_21,"What is the JavaScript event loop?",single_choice,3,hard,"javascript,event-loop,async"
js_22,"Which advanced JavaScript patterns improve code organization?",multiple_choice,3,hard,"javascript,patterns,architecture"
js_23,"What is the difference between call, apply, and bind?",single_choice,3,hard,"javascript,context,methods"
js_24,"Which JavaScript memory management concepts are important?",multiple_choice,3,hard,"javascript,memory,garbage-collection"
js_25,"What is the purpose of JavaScript Proxies?",single_choice,3,hard,"javascript,proxies,metaprogramming"
js_26,"Which JavaScript engine optimizations affect performance?",multiple_choice,3,hard,"javascript,performance,optimization"
js_27,"What is the difference between microtasks and macrotasks?",single_choice,3,hard,"javascript,event-loop,tasks"
js_28,"Which JavaScript features support metaprogramming?",multiple_choice,3,hard,"javascript,metaprogramming,reflection"
js_29,"What is the purpose of WeakMap and WeakSet?",single_choice,3,hard,"javascript,weakmap,memory"
js_30,"Which JavaScript concurrency patterns are advanced?",multiple_choice,3,hard,"javascript,concurrency,async"
