question_id,option_text,is_correct,order_index
pl_1,"A structure that repeats a block of code",true,1
pl_1,"A type of variable",false,2
pl_1,"A function",false,3
pl_1,"A data type",false,4
pl_2,"A container for storing data values",true,1
pl_2,"A type of loop",false,2
pl_2,"A function",false,3
pl_2,"A programming language",false,4
pl_3,"Sequence",true,1
pl_3,"Selection",true,2
pl_3,"Compilation",false,3
pl_3,"Iteration",true,4
pl_4,"A step-by-step procedure to solve a problem",true,1
pl_4,"A programming language",false,2
pl_4,"A type of variable",false,3
pl_4,"A computer program",false,4
pl_5,"A reusable block of code that performs a specific task",true,1
pl_5,"A type of variable",false,2
pl_5,"A loop structure",false,3
pl_5,"A data type",false,4
pl_6,"Integer",true,1
pl_6,"String",true,2
pl_6,"Loop",false,3
pl_6,"Boolean",true,4
pl_7,"The process of finding and fixing errors in code",true,1
pl_7,"Writing new code",false,2
pl_7,"Compiling code",false,3
pl_7,"Running code",false,4
pl_8,"An informal high-level description of a program's logic",true,1
pl_8,"A programming language",false,2
pl_8,"A type of error",false,3
pl_8,"A debugging tool",false,4
pl_9,"Procedural",true,1
pl_9,"Object-oriented",true,2
pl_9,"Debugging",false,3
pl_9,"Functional",true,4
pl_10,"A collection of elements stored in contiguous memory locations",true,1
pl_10,"A type of loop",false,2
pl_10,"A function",false,3
pl_10,"A variable",false,4
pl_11,"O(log n)",true,1
pl_11,"O(n)",false,2
pl_11,"O(n²)",false,3
pl_11,"O(1)",false,4
pl_12,"Stack",true,1
pl_12,"Queue",false,2
pl_12,"Call Stack",true,3
pl_12,"Array",false,4
pl_13,"A function that calls itself",true,1
pl_13,"A type of loop",false,2
pl_13,"A data structure",false,3
pl_13,"A sorting algorithm",false,4
pl_14,"Merge Sort",true,1
pl_14,"Bubble Sort",false,2
pl_14,"Quick Sort",true,3
pl_14,"Heap Sort",true,4
pl_15,"BFS explores level by level, DFS explores as deep as possible first",true,1
pl_15,"DFS explores level by level, BFS explores as deep as possible first",false,2
pl_15,"They are exactly the same",false,3
pl_15,"BFS is faster than DFS",false,4
pl_16,"Encapsulation",true,1
pl_16,"Inheritance",true,2
pl_16,"Global variables",false,3
pl_16,"Polymorphism",true,4
pl_17,"To track changes in code and collaborate with others",true,1
pl_17,"To compile code faster",false,2
pl_17,"To debug programs",false,3
pl_17,"To optimize performance",false,4
pl_18,"Singleton",true,1
pl_18,"Observer",true,2
pl_18,"Global variable",false,3
pl_18,"Factory",true,4
pl_19,"Compilation translates to machine code, interpretation executes directly",true,1
pl_19,"Interpretation translates to machine code, compilation executes directly",false,2
pl_19,"They are exactly the same",false,3
pl_19,"Compilation is always faster",false,4
pl_20,"SOLID principles",true,1
pl_20,"DRY principle",true,2
pl_20,"Global variables",false,3
pl_20,"Separation of concerns",true,4
pl_21,"To solve optimization problems by breaking them into subproblems",true,1
pl_21,"To make programs run faster",false,2
pl_21,"To use less memory",false,3
pl_21,"To simplify code",false,4
pl_22,"Dijkstra's algorithm",true,1
pl_22,"Bubble sort",false,2
pl_22,"Bellman-Ford algorithm",true,3
pl_22,"Linear search",false,4
pl_23,"P problems can be solved in polynomial time, NP problems can be verified in polynomial time",true,1
pl_23,"NP problems can be solved in polynomial time, P problems can be verified in polynomial time",false,2
pl_23,"P and NP are exactly the same",false,3
pl_23,"P problems are harder than NP problems",false,4
pl_24,"Trie",true,1
pl_24,"B-tree",true,2
pl_24,"Array",false,3
pl_24,"Bloom filter",true,4
pl_25,"To improve program performance by transforming code",true,1
pl_25,"To make code easier to read",false,2
pl_25,"To reduce file size",false,3
pl_25,"To add debugging information",false,4
pl_26,"CAP theorem",true,1
pl_26,"Eventual consistency",true,2
pl_26,"Single point of failure",false,3
pl_26,"Fault tolerance",true,4
pl_27,"You can only guarantee 2 of 3: Consistency, Availability, Partition tolerance",true,1
pl_27,"All three properties can always be guaranteed",false,2
pl_27,"Only one property can be guaranteed at a time",false,3
pl_27,"CAP stands for Create, Alter, Partition",false,4
pl_28,"Immutability",true,1
pl_28,"Pure functions",true,2
pl_28,"Global state",false,3
pl_28,"Higher-order functions",true,4
pl_29,"To execute multiple tasks simultaneously or in overlapping time periods",true,1
pl_29,"To make programs run faster",false,2
pl_29,"To use less memory",false,3
pl_29,"To simplify code",false,4
pl_30,"To enable computers to learn and make predictions from data",true,1
pl_30,"To make programs run faster",false,2
pl_30,"To reduce memory usage",false,3
pl_30,"To simplify programming",false,4
