question_id,text,type,points,difficulty,tags
pl_1,"What is a loop in programming?",single_choice,1,easy,"programming,loops,basics"
pl_2,"What is a variable in programming?",single_choice,1,easy,"programming,variables,basics"
pl_3,"Which are basic programming constructs?",multiple_choice,1,easy,"programming,constructs,basics"
pl_4,"What is an algorithm?",single_choice,1,easy,"programming,algorithms,basics"
pl_5,"What is a function in programming?",single_choice,1,easy,"programming,functions,basics"
pl_6,"Which are common data types in programming?",multiple_choice,1,easy,"programming,data-types,basics"
pl_7,"What is debugging?",single_choice,1,easy,"programming,debugging,basics"
pl_8,"What is pseudocode?",single_choice,1,easy,"programming,pseudocode,basics"
pl_9,"Which are types of programming paradigms?",multiple_choice,1,easy,"programming,paradigms,basics"
pl_10,"What is an array?",single_choice,1,easy,"programming,arrays,data-structures"
pl_11,"What is the time complexity of binary search?",single_choice,2,normal,"algorithms,complexity,search"
pl_12,"Which data structures use LIFO principle?",multiple_choice,2,normal,"data-structures,stack,lifo"
pl_13,"What is recursion?",single_choice,2,normal,"programming,recursion,algorithms"
pl_14,"Which sorting algorithms have O(n log n) average time complexity?",multiple_choice,2,normal,"algorithms,sorting,complexity"
pl_15,"What is the difference between breadth-first and depth-first search?",single_choice,2,normal,"algorithms,graph,search"
pl_16,"Which are characteristics of object-oriented programming?",multiple_choice,2,normal,"programming,oop,paradigms"
pl_17,"What is the purpose of version control systems?",single_choice,2,normal,"programming,version-control,tools"
pl_18,"Which are common design patterns?",multiple_choice,2,normal,"programming,design-patterns,architecture"
pl_19,"What is the difference between compilation and interpretation?",single_choice,2,normal,"programming,compilation,interpretation"
pl_20,"Which are principles of good software design?",multiple_choice,2,normal,"programming,design,principles"
pl_21,"What is the purpose of dynamic programming?",single_choice,3,hard,"algorithms,dynamic-programming,optimization"
pl_22,"Which graph algorithms solve shortest path problems?",multiple_choice,3,hard,"algorithms,graph,shortest-path"
pl_23,"What is the difference between P and NP complexity classes?",single_choice,3,hard,"algorithms,complexity,theory"
pl_24,"Which are advanced data structures for specific use cases?",multiple_choice,3,hard,"data-structures,advanced,specialized"
pl_25,"What is the purpose of compiler optimization?",single_choice,3,hard,"programming,compilers,optimization"
pl_26,"Which are principles of distributed systems design?",multiple_choice,3,hard,"programming,distributed,systems"
pl_27,"What is the CAP theorem in distributed systems?",single_choice,3,hard,"programming,distributed,cap-theorem"
pl_28,"Which are characteristics of functional programming?",multiple_choice,3,hard,"programming,functional,paradigms"
pl_29,"What is the purpose of concurrent programming?",single_choice,3,hard,"programming,concurrency,parallelism"
pl_30,"What is the purpose of machine learning algorithms?",single_choice,3,hard,"algorithms,machine-learning,ai"
