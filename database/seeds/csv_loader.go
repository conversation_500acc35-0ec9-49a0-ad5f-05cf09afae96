package seeds

import (
	"encoding/csv"
	"etuto-database/models"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

type QuestionData struct {
	ID         string
	Text       string
	Type       models.QuestionType
	Points     float64
	Difficulty models.DifficultyLevel
	Tags       string
}

type OptionData struct {
	QuestionID string
	Text       string
	IsCorrect  bool
	OrderIndex int
}

func loadQuestionsFromCSV(filename string) ([]QuestionData, error) {
	file, err := os.Open(filepath.Join("seeds", "csv", filename))
	if err != nil {
		return nil, fmt.E<PERSON><PERSON>("failed to open CSV file %s: %v", filename, err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV file %s: %v", filename, err)
	}

	var questions []QuestionData
	for i, record := range records {
		if i == 0 { // Skip header
			continue
		}

		if len(record) < 6 {
			continue
		}

		points, err := strconv.ParseFloat(record[3], 64)
		if err != nil {
			return nil, fmt.Errorf("invalid points value in row %d: %v", i+1, err)
		}

		var questionType models.QuestionType
		switch record[2] {
		case "single_choice":
			questionType = models.QuestionTypeSingleChoice
		case "multiple_choice":
			questionType = models.QuestionTypeMultipleChoice
		default:
			questionType = models.QuestionTypeSingleChoice
		}

		var difficulty models.DifficultyLevel
		switch record[4] {
		case "easy":
			difficulty = models.DifficultyEasy
		case "normal":
			difficulty = models.DifficultyNormal
		case "hard":
			difficulty = models.DifficultyHard
		default:
			difficulty = models.DifficultyEasy
		}

		questions = append(questions, QuestionData{
			ID:         record[0],
			Text:       record[1],
			Type:       questionType,
			Points:     points,
			Difficulty: difficulty,
			Tags:       record[5],
		})
	}

	return questions, nil
}

func loadOptionsFromCSV(filename string) ([]OptionData, error) {
	file, err := os.Open(filepath.Join("seeds", "csv", filename))
	if err != nil {
		return nil, fmt.Errorf("failed to open CSV file %s: %v", filename, err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("failed to read CSV file %s: %v", filename, err)
	}

	var options []OptionData
	for i, record := range records {
		if i == 0 { // Skip header
			continue
		}

		if len(record) < 4 {
			continue
		}

		isCorrect := strings.ToLower(record[2]) == "true"
		orderIndex, err := strconv.Atoi(record[3])
		if err != nil {
			return nil, fmt.Errorf("invalid order_index value in row %d: %v", i+1, err)
		}

		options = append(options, OptionData{
			QuestionID: record[0],
			Text:       record[1],
			IsCorrect:  isCorrect,
			OrderIndex: orderIndex,
		})
	}

	return options, nil
}

func loadCategoryQuestions(categoryName string) ([]QuestionData, []OptionData, error) {
	var questionsFile, optionsFile string

	switch categoryName {
	case "HTML Fundamentals":
		questionsFile = "html_questions.csv"
		optionsFile = "html_options.csv"
	case "CSS Styling":
		questionsFile = "css_questions.csv"
		optionsFile = "css_options.csv"
	case "JavaScript Basics":
		questionsFile = "javascript_questions.csv"
		optionsFile = "javascript_options.csv"
	case "Programming Logic":
		questionsFile = "programming_logic_questions.csv"
		optionsFile = "programming_logic_options.csv"
	case "Database Concepts":
		questionsFile = "database_questions.csv"
		optionsFile = "database_options.csv"
	default:
		return nil, nil, fmt.Errorf("unknown category: %s", categoryName)
	}

	questions, err := loadQuestionsFromCSV(questionsFile)
	if err != nil {
		return nil, nil, err
	}

	options, err := loadOptionsFromCSV(optionsFile)
	if err != nil {
		return nil, nil, err
	}

	return questions, options, nil
}
