package seeds

import (
	"etuto-database/models"
	"fmt"

	"gorm.io/gorm"
)

func seedQuestionBank(db *gorm.DB, courses []models.Course, teacher models.User) error {
	if len(courses) == 0 {
		return nil
	}

	course1 := courses[0] // Introduction to Web Development

	// Create Question Bank Categories
	categories := []struct {
		Name        string
		Description string
	}{
		{
			Name:        "HTML Fundamentals",
			Description: "Basic HTML concepts and syntax",
		},
		{
			Name:        "CSS Styling",
			Description: "CSS properties, selectors, and layout",
		},
		{
			Name:        "JavaScript Basics",
			Description: "JavaScript fundamentals and DOM manipulation",
		},
		{
			Name:        "Programming Logic",
			Description: "Basic programming concepts and algorithms",
		},
		{
			Name:        "Database Concepts",
			Description: "Database design and SQL fundamentals",
		},
	}

	var createdCategories []models.QuestionBankCategory
	for _, catData := range categories {
		var category models.QuestionBankCategory
		if err := db.Where("name = ? AND course_id = ?", catData.Name, course1.ID).First(&category).Error; err != nil {
			category = models.QuestionBankCategory{
				Name:        catData.Name,
				Description: catData.Description,
				CourseID:    &course1.ID,
			}
			if err := db.Create(&category).Error; err != nil {
				return err
			}
		}
		createdCategories = append(createdCategories, category)
	}

	// Create Bank Questions for each category
	for _, category := range createdCategories {
		if err := createBankQuestionsForCategory(db, category, teacher.ID); err != nil {
			return err
		}
	}

	return nil
}

func createBankQuestionsForCategory(db *gorm.DB, category models.QuestionBankCategory, createdBy uint) error {
	// Load questions and options from CSV files
	questions, options, err := loadCategoryQuestions(category.Name)
	if err != nil {
		return fmt.Errorf("failed to load questions for category %s: %v", category.Name, err)
	}

	// Create a map of options by question ID for easy lookup
	optionsByQuestion := make(map[string][]OptionData)
	for _, option := range options {
		optionsByQuestion[option.QuestionID] = append(optionsByQuestion[option.QuestionID], option)
	}

	for _, questionData := range questions {
		var bankQuestion models.BankQuestion
		if err := db.Where("category_id = ? AND question_text = ?", category.ID, questionData.Text).First(&bankQuestion).Error; err != nil {
			bankQuestion = models.BankQuestion{
				CategoryID:   category.ID,
				QuestionText: questionData.Text,
				QuestionType: questionData.Type,
				Points:       questionData.Points,
				Difficulty:   questionData.Difficulty,
				Tags:         questionData.Tags,
				CreatedBy:    createdBy,
			}
			if err := db.Create(&bankQuestion).Error; err != nil {
				return err
			}

			// Create options for the bank question
			questionOptions := optionsByQuestion[questionData.ID]
			for _, optionData := range questionOptions {
				option := models.BankOption{
					BankQuestionID: bankQuestion.ID,
					OptionText:     optionData.Text,
					IsCorrect:      optionData.IsCorrect,
					OrderIndex:     optionData.OrderIndex,
				}
				if err := db.Create(&option).Error; err != nil {
					return err
				}
			}
		}
	}

	return nil
}
