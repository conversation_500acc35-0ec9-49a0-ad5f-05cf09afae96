package seeds

import (
	"etuto-database/models"

	"gorm.io/gorm"
)

func seedQuizRules(db *gorm.DB, quizzes []models.Quiz) error {
	if len(quizzes) == 0 {
		return nil
	}

	// Get question bank categories for the course
	var categories []models.QuestionBankCategory
	if err := db.Where("course_id = ?", quizzes[0].CourseID).Find(&categories).Error; err != nil {
		return err
	}

	if len(categories) == 0 {
		return nil // No categories to create rules for
	}

	// Create category map for easier lookup
	categoryMap := make(map[string]models.QuestionBankCategory)
	for _, cat := range categories {
		categoryMap[cat.Name] = cat
	}

	// Create quiz rules for each quiz
	for _, quiz := range quizzes {
		if err := createQuizRulesForQuiz(db, quiz, categoryMap); err != nil {
			return err
		}
	}

	return nil
}

func createQuizRulesForQuiz(db *gorm.DB, quiz models.Quiz, categoryMap map[string]models.QuestionBankCategory) error {
	// Define rules based on quiz difficulty and title
	var rules []QuizRuleData

	switch quiz.Difficulty {
	case models.DifficultyEasy:
		rules = getEasyQuizRules(categoryMap)
	case models.DifficultyNormal:
		rules = getNormalQuizRules(categoryMap)
	case models.DifficultyHard:
		rules = getHardQuizRules(categoryMap)
	default:
		rules = getNormalQuizRules(categoryMap) // Default to normal
	}

	// Create the rules
	for i, ruleData := range rules {
		var existingRule models.QuizRule
		if err := db.Where("quiz_id = ? AND category_id = ? AND difficulty = ? AND question_type = ?",
			quiz.ID, ruleData.CategoryID, ruleData.Difficulty, ruleData.QuestionType).First(&existingRule).Error; err != nil {

			rule := models.QuizRule{
				QuizID:       quiz.ID,
				CategoryID:   ruleData.CategoryID,
				Difficulty:   ruleData.Difficulty,
				QuestionType: ruleData.QuestionType,
				Percentage:   ruleData.Percentage,
				OrderIndex:   i + 1,
			}

			if err := db.Create(&rule).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

type QuizRuleData struct {
	CategoryID   uint
	Difficulty   models.DifficultyLevel
	QuestionType models.QuestionType
	Count        int
	Percentage   float64
}

func getEasyQuizRules(categoryMap map[string]models.QuestionBankCategory) []QuizRuleData {
	var rules []QuizRuleData

	// HTML Fundamentals - Easy questions (50% total)
	if htmlCat, exists := categoryMap["HTML Fundamentals"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   htmlCat.ID,
			Difficulty:   models.DifficultyEasy,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,    // Not used when percentage is set
			Percentage:   40.0, // 40% of total questions
		})
		rules = append(rules, QuizRuleData{
			CategoryID:   htmlCat.ID,
			Difficulty:   models.DifficultyEasy,
			QuestionType: models.QuestionTypeMultipleChoice,
			Count:        0,
			Percentage:   10.0, // 10% of total questions
		})
	}

	// CSS Styling - Easy questions (30% total)
	if cssCat, exists := categoryMap["CSS Styling"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   cssCat.ID,
			Difficulty:   models.DifficultyEasy,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   30.0, // 30% of total questions
		})
	}

	// Programming Logic - Easy questions (20% total)
	if progCat, exists := categoryMap["Programming Logic"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   progCat.ID,
			Difficulty:   models.DifficultyEasy,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   20.0, // 20% of total questions
		})
	}

	return rules
}

func getNormalQuizRules(categoryMap map[string]models.QuestionBankCategory) []QuizRuleData {
	var rules []QuizRuleData

	// HTML Fundamentals - Mix of easy and normal (40% total)
	if htmlCat, exists := categoryMap["HTML Fundamentals"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   htmlCat.ID,
			Difficulty:   models.DifficultyEasy,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   20.0, // 20% of total questions
		})
		rules = append(rules, QuizRuleData{
			CategoryID:   htmlCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   20.0, // 20% of total questions
		})
	}

	// CSS Styling - Normal questions (30% total)
	if cssCat, exists := categoryMap["CSS Styling"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   cssCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeMultipleChoice,
			Count:        0,
			Percentage:   15.0, // 15% of total questions
		})
		rules = append(rules, QuizRuleData{
			CategoryID:   cssCat.ID,
			Difficulty:   models.DifficultyEasy,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   15.0, // 15% of total questions
		})
	}

	// JavaScript Basics - Normal questions (20% total)
	if jsCat, exists := categoryMap["JavaScript Basics"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   jsCat.ID,
			Difficulty:   models.DifficultyEasy,
			QuestionType: models.QuestionTypeMultipleChoice,
			Count:        0,
			Percentage:   10.0, // 10% of total questions
		})
		rules = append(rules, QuizRuleData{
			CategoryID:   jsCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   10.0, // 10% of total questions
		})
	}

	// Programming Logic - Normal questions (10% total)
	if progCat, exists := categoryMap["Programming Logic"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   progCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   10.0, // 10% of total questions
		})
	}

	return rules
}

func getHardQuizRules(categoryMap map[string]models.QuestionBankCategory) []QuizRuleData {
	var rules []QuizRuleData

	// HTML Fundamentals - Mix of all difficulties (30% total)
	if htmlCat, exists := categoryMap["HTML Fundamentals"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   htmlCat.ID,
			Difficulty:   models.DifficultyEasy,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   10.0, // 10% of total questions
		})
		rules = append(rules, QuizRuleData{
			CategoryID:   htmlCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   10.0, // 10% of total questions
		})
		rules = append(rules, QuizRuleData{
			CategoryID:   htmlCat.ID,
			Difficulty:   models.DifficultyHard,
			QuestionType: models.QuestionTypeMultipleChoice,
			Count:        0,
			Percentage:   10.0, // 10% of total questions
		})
	}

	// CSS Styling - Hard questions (30% total)
	if cssCat, exists := categoryMap["CSS Styling"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   cssCat.ID,
			Difficulty:   models.DifficultyHard,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   15.0, // 15% of total questions
		})
		rules = append(rules, QuizRuleData{
			CategoryID:   cssCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeMultipleChoice,
			Count:        0,
			Percentage:   15.0, // 15% of total questions
		})
	}

	// JavaScript Basics - Hard questions (15% total)
	if jsCat, exists := categoryMap["JavaScript Basics"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   jsCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   15.0, // 15% of total questions
		})
	}

	// Programming Logic - Hard questions (15% total)
	if progCat, exists := categoryMap["Programming Logic"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   progCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   15.0, // 15% of total questions
		})
	}

	// Database Concepts - Hard questions (10% total)
	if dbCat, exists := categoryMap["Database Concepts"]; exists {
		rules = append(rules, QuizRuleData{
			CategoryID:   dbCat.ID,
			Difficulty:   models.DifficultyNormal,
			QuestionType: models.QuestionTypeSingleChoice,
			Count:        0,
			Percentage:   10.0, // 10% of total questions
		})
	}

	return rules
}
