package seeds

import (
	"etuto-database/models"

	"gorm.io/gorm"
)

func seedQuizzes(db *gorm.DB, courses []models.Course, categories []models.Category) ([]models.Quiz, error) {
	if len(courses) == 0 || len(categories) == 0 {
		return nil, nil
	}

	var createdQuizzes []models.Quiz

	// Find categories by name for easier reference
	categoryMap := make(map[string]models.Category)
	for _, cat := range categories {
		categoryMap[cat.Name] = cat
	}

	course1 := courses[0] // Introduction to Web Development

	// Create quizzes for different difficulty levels and categories
	// Note: We no longer create questions directly in quizzes
	// Instead, we'll create QuizRules that reference BankQuestions
	quizData := []struct {
		Title          string
		Description    string
		Difficulty     models.DifficultyLevel
		CategoryName   string
		TimeLimit      int
		PassScore      float64
		TotalQuestions int
	}{
		{
			Title:          "HTML Basics - Easy",
			Description:    "Basic HTML concepts for beginners",
			Difficulty:     models.DifficultyEasy,
			CategoryName:   "Web Development",
			TimeLimit:      10,
			PassScore:      60,
			TotalQuestions: 10,
		},
		{
			Title:          "HTML Intermediate",
			Description:    "Intermediate HTML concepts and best practices",
			Difficulty:     models.DifficultyNormal,
			CategoryName:   "Web Development",
			TimeLimit:      15,
			PassScore:      70,
			TotalQuestions: 10,
		},
		{
			Title:          "HTML Advanced",
			Description:    "Advanced HTML5 features and semantic markup",
			Difficulty:     models.DifficultyHard,
			CategoryName:   "Web Development",
			TimeLimit:      20,
			PassScore:      80,
			TotalQuestions: 10,
		},
		{
			Title:          "Programming Basics - Easy",
			Description:    "Basic programming concepts for beginners",
			Difficulty:     models.DifficultyEasy,
			CategoryName:   "Programming",
			TimeLimit:      15,
			PassScore:      60,
			TotalQuestions: 10,
		},
		{
			Title:          "Programming Intermediate",
			Description:    "Intermediate programming concepts and algorithms",
			Difficulty:     models.DifficultyNormal,
			CategoryName:   "Programming",
			TimeLimit:      20,
			PassScore:      70,
			TotalQuestions: 10,
		},
		{
			Title:          "Programming Advanced",
			Description:    "Advanced programming patterns and optimization",
			Difficulty:     models.DifficultyHard,
			CategoryName:   "Programming",
			TimeLimit:      25,
			PassScore:      80,
			TotalQuestions: 10,
		},
		{
			Title:          "Database Basics - Easy",
			Description:    "Basic database concepts and SQL",
			Difficulty:     models.DifficultyEasy,
			CategoryName:   "Database",
			TimeLimit:      15,
			PassScore:      60,
			TotalQuestions: 10,
		},
		{
			Title:          "Database Intermediate",
			Description:    "Intermediate database design and optimization",
			Difficulty:     models.DifficultyNormal,
			CategoryName:   "Database",
			TimeLimit:      20,
			PassScore:      70,
			TotalQuestions: 10,
		},
		{
			Title:          "Database Advanced",
			Description:    "Advanced database administration and performance",
			Difficulty:     models.DifficultyHard,
			CategoryName:   "Database",
			TimeLimit:      25,
			PassScore:      80,
			TotalQuestions: 10,
		},
		{
			Title:          "An toàn thông tin",
			Description:    "Chương trình nâng cao nhận thức An toàn thông tin và Phòng chống thất thoát dữ liệu	",
			Difficulty:     models.DifficultyHard,
			CategoryName:   "Cybersecurity",
			TimeLimit:      25,
			PassScore:      80,
			TotalQuestions: 10,
		},
	}

	// Create quizzes and their questions
	for _, quizInfo := range quizData {
		category, exists := categoryMap[quizInfo.CategoryName]
		if !exists {
			continue
		}

		var quiz models.Quiz
		if err := db.Where("course_id = ? AND title = ?", course1.ID, quizInfo.Title).First(&quiz).Error; err != nil {
			quiz = models.Quiz{
				CourseID:          course1.ID,
				Title:             quizInfo.Title,
				Description:       quizInfo.Description,
				TimeLimit:         quizInfo.TimeLimit,
				PassScore:         quizInfo.PassScore,
				IsPublished:       true,
				Difficulty:        quizInfo.Difficulty,
				CategoryID:        &category.ID,
				TotalQuestions:    quizInfo.TotalQuestions,
				MaxAttempts:       0, // Unlimited attempts
				ShowResults:       true,
				ShowCorrectAnswer: true, // Enable showing correct answers for all quizzes
				AllowReview:       true,
			}
			if err := db.Create(&quiz).Error; err != nil {
				return nil, err
			}
		}

		createdQuizzes = append(createdQuizzes, quiz)

		// QuizRules will be created separately by quiz_rules_seeds.go
		// No need to create questions directly anymore
	}

	return createdQuizzes, nil
}

// Note: All question data has been moved to question_bank_seeds.go
// QuizRules will be created by quiz_rules_seeds.go to link quizzes with bank questions
