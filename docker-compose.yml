version: '3.8'

services:
  postgres:
    image: postgres:17-alpine
    environment:
      POSTGRES_DB: etuto_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    ports:
      - "8080:8080"
    environment:
      DATABASE_URL: ******************************************/etuto_dev?sslmode=disable
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      PORT: 8080
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./backend:/app/backend
      - ./database:/app/database

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      VITE_API_URL: http://localhost:8080/api
    volumes:
      - ./frontend:/app
      - /app/node_modules
    working_dir: /app

volumes:
  postgres_data:
