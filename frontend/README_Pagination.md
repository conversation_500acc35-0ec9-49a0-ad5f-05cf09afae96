# Pagination Components

H<PERSON> thống pagination có thể tái sử dụng cho tất cả các trang có phân trang.

## Components

### 1. PagingComponent
Component chính để hiển thị các nút phân trang với logic thông minh chỉ hiển thị tối đa 10 steps.

### 2. PaginationInfo
Component hiển thị thông tin về pagination (số items hiện tại, tổng số items, trang hiện tại).

## Cách sử dụng

### Import components
```tsx
import { PagingComponent, PaginationInfo } from '../components';
```

### Sử dụng cơ bản
```tsx
const [currentPage, setCurrentPage] = useState(1);
const [totalPages, setTotalPages] = useState(0);
const [totalItems, setTotalItems] = useState(0);
const itemsPerPage = 10;

// Trong JSX
<PagingComponent
  currentPage={currentPage}
  totalPages={totalPages}
  onPageChange={setCurrentPage}
  maxVisiblePages={10}
/>
```

### Sử dụng với thông tin pagination
```tsx
<div className="d-flex justify-content-between align-items-center mt-4">
  <PaginationInfo
    currentPage={currentPage}
    totalPages={totalPages}
    totalItems={totalItems}
    itemsPerPage={itemsPerPage}
  />
  <PagingComponent
    currentPage={currentPage}
    totalPages={totalPages}
    onPageChange={setCurrentPage}
    maxVisiblePages={10}
  />
</div>
```

## Props

### PagingComponent Props
- `currentPage` (number): Trang hiện tại
- `totalPages` (number): Tổng số trang
- `onPageChange` (function): Callback khi thay đổi trang
- `maxVisiblePages` (number, optional): Số trang tối đa hiển thị (default: 10)
- `showFirstLast` (boolean, optional): Hiển thị nút đầu/cuối (default: true)
- `showPrevNext` (boolean, optional): Hiển thị nút trước/sau (default: true)
- `size` ('sm' | 'md' | 'lg', optional): Kích thước pagination (default: 'md')
- `className` (string, optional): CSS class bổ sung

### PaginationInfo Props
- `currentPage` (number): Trang hiện tại
- `totalPages` (number): Tổng số trang
- `totalItems` (number): Tổng số items
- `itemsPerPage` (number): Số items mỗi trang
- `className` (string, optional): CSS class bổ sung
- `showItemRange` (boolean, optional): Hiển thị range items (default: true)
- `showTotalPages` (boolean, optional): Hiển thị tổng số trang (default: true)
- `showTotalItems` (boolean, optional): Hiển thị tổng số items (default: true)

## Tính năng

### PagingComponent
- ✅ Hiển thị tối đa 10 trang (có thể tùy chỉnh)
- ✅ Tự động thêm "..." khi có quá nhiều trang
- ✅ Nút First/Last page
- ✅ Nút Previous/Next page
- ✅ Responsive design
- ✅ Accessibility support (ARIA labels)
- ✅ Tự động ẩn khi chỉ có 1 trang
- ✅ Hỗ trợ nhiều kích thước (sm, md, lg)

### PaginationInfo
- ✅ Hiển thị range items hiện tại (vd: "Hiển thị 1-10")
- ✅ Hiển thị tổng số items (vd: "trong tổng số 150 mục")
- ✅ Hiển thị trang hiện tại (vd: "(Trang 1/15)")
- ✅ Tự động xử lý trường hợp không có dữ liệu

## Ví dụ thực tế

Xem file `frontend/src/pages/QuestionBank.tsx` để tham khảo cách implement đầy đủ.

## Logic hiển thị trang

### Khi totalPages <= maxVisiblePages (10)
Hiển thị tất cả các trang: `[1] [2] [3] [4] [5]`

### Khi totalPages > maxVisiblePages
- Nếu currentPage gần đầu: `[1] [2] [3] [4] [5] ... [20]`
- Nếu currentPage ở giữa: `[1] ... [8] [9] [10] [11] [12] ... [20]`
- Nếu currentPage gần cuối: `[1] ... [16] [17] [18] [19] [20]`

## Styling

Components sử dụng Bootstrap classes, có thể tùy chỉnh thêm CSS nếu cần:

```css
.pagination {
  /* Custom styles */
}

.page-item.active .page-link {
  /* Active page styles */
}

.page-item.disabled .page-link {
  /* Disabled page styles */
}
```
