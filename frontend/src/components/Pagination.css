/* Pagination Component Styles */

/* Custom pagination styling */
.pagination {
  margin-bottom: 0;
}

.pagination .page-link {
  border: 1px solid #dee2e6;
  color: #6c757d;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  text-decoration: none;
  background-color: #fff;
  border-radius: 0;
  transition: all 0.15s ease-in-out;
}

.pagination .page-link:hover {
  z-index: 2;
  color: #0056b3;
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.pagination .page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.pagination .page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.pagination .page-item:last-child .page-link {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.pagination .page-item.active .page-link {
  z-index: 3;
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
  border-color: #dee2e6;
  opacity: 0.65;
}

/* Small pagination */
.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

/* Large pagination */
.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
}

.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

/* Ellipsis styling */
.pagination .page-item.disabled .page-link {
  cursor: default;
}

/* Icon buttons styling */
.pagination .page-link svg {
  vertical-align: middle;
}

/* Responsive pagination */
@media (max-width: 576px) {
  .pagination {
    justify-content: center;
  }
  
  .pagination .page-item {
    margin: 0 1px;
  }
  
  .pagination .page-link {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
  }
  
  /* Hide some page numbers on mobile */
  .pagination .page-item:not(.active):not(:first-child):not(:last-child):not(.disabled) {
    display: none;
  }
  
  .pagination .page-item:nth-child(-n+3),
  .pagination .page-item:nth-last-child(-n+3) {
    display: flex !important;
  }
}

/* Pagination info styling */
.pagination-info {
  color: #6c757d;
  font-size: 0.875rem;
  margin: 0;
}

/* Animation for page transitions */
.pagination .page-link {
  position: relative;
  overflow: hidden;
}

.pagination .page-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.pagination .page-link:hover::before {
  left: 100%;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .pagination .page-link {
    background-color: #343a40;
    border-color: #495057;
    color: #adb5bd;
  }
  
  .pagination .page-link:hover {
    background-color: #495057;
    border-color: #6c757d;
    color: #fff;
  }
  
  .pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }
  
  .pagination .page-item.disabled .page-link {
    background-color: #343a40;
    border-color: #495057;
    color: #6c757d;
  }
  
  .pagination-info {
    color: #adb5bd;
  }
}

/* Focus styles for accessibility */
.pagination .page-link:focus-visible {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
  box-shadow: none;
}

/* Loading state */
.pagination.loading {
  opacity: 0.6;
  pointer-events: none;
}

.pagination.loading .page-link {
  cursor: wait;
}
