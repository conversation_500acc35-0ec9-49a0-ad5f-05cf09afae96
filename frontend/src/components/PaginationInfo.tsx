import React from 'react';

interface PaginationInfoProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  className?: string;
  showItemRange?: boolean;
  showTotalPages?: boolean;
  showTotalItems?: boolean;
}

const PaginationInfo: React.FC<PaginationInfoProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  className = '',
  showItemRange = true,
  showTotalPages = true,
  showTotalItems = true
}) => {
  // Tính toán range của items hiện tại
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  if (totalItems === 0) {
    return (
      <div className={`text-muted small ${className}`}>
        Không có dữ liệu
      </div>
    );
  }

  const infoItems: string[] = [];

  if (showItemRange) {
    infoItems.push(`Hiển thị ${startItem}-${endItem}`);
  }

  if (showTotalItems) {
    infoItems.push(`trong tổng số ${totalItems} mục`);
  }

  if (showTotalPages && totalPages > 1) {
    infoItems.push(`(Trang ${currentPage}/${totalPages})`);
  }

  return (
    <div className={`text-muted small ${className}`}>
      {infoItems.join(' ')}
    </div>
  );
};

export default PaginationInfo;
