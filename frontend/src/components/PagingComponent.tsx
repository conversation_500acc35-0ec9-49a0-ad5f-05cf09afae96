import React from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import './Pagination.css';

interface PagingComponentProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  maxVisiblePages?: number;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const PagingComponent: React.FC<PagingComponentProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  maxVisiblePages = 10,
  showFirstLast = true,
  showPrevNext = true,
  size = 'md',
  className = ''
}) => {
  // Không hiển thị pagination nếu chỉ có 1 trang hoặc ít hơn
  if (totalPages <= 1) {
    return null;
  }

  // Tính toán range của các trang sẽ hiển thị
  const getVisiblePages = (): number[] => {
    const pages: number[] = [];

    if (totalPages <= maxVisiblePages) {
      // Nếu tổng số trang <= maxVisiblePages, hiển thị tất cả
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Tính toán để hiển thị currentPage ở giữa nếu có thể
      const halfVisible = Math.floor(maxVisiblePages / 2);
      let startPage = Math.max(1, currentPage - halfVisible);
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      // Điều chỉnh nếu endPage đã đạt tối đa
      if (endPage === totalPages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  const visiblePages = getVisiblePages();
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages;

  // Xác định size class cho pagination
  const sizeClass = size === 'sm' ? 'pagination-sm' : size === 'lg' ? 'pagination-lg' : '';

  return (
    <div className={`d-flex justify-content-center ${className}`}>
      <nav aria-label="Pagination Navigation">
        <ul className={`pagination ${sizeClass} mb-0`}>
          {/* First Page Button */}
          {showFirstLast && !isFirstPage && (
            <li className="page-item">
              <button
                className="page-link"
                onClick={() => onPageChange(1)}
                aria-label="Trang đầu"
                title="Trang đầu"
              >
                <ChevronsLeft size={16} />
              </button>
            </li>
          )}

          {/* Previous Page Button */}
          {showPrevNext && (
            <li className={`page-item ${isFirstPage ? 'disabled' : ''}`}>
              <button
                className="page-link"
                onClick={() => onPageChange(currentPage - 1)}
                disabled={isFirstPage}
                aria-label="Trang trước"
                title="Trang trước"
              >
                <ChevronLeft size={16} />
              </button>
            </li>
          )}

          {/* Ellipsis before visible pages (if needed) */}
          {visiblePages[0] > 1 && (
            <>
              <li className="page-item">
                <button
                  className="page-link"
                  onClick={() => onPageChange(1)}
                >
                  1
                </button>
              </li>
              {visiblePages[0] > 2 && (
                <li className="page-item disabled">
                  <span className="page-link">...</span>
                </li>
              )}
            </>
          )}

          {/* Visible Page Numbers */}
          {visiblePages.map((page) => (
            <li
              key={page}
              className={`page-item ${currentPage === page ? 'active' : ''}`}
            >
              <button
                className="page-link"
                onClick={() => onPageChange(page)}
                aria-label={`Trang ${page}`}
                aria-current={currentPage === page ? 'page' : undefined}
              >
                {page}
              </button>
            </li>
          ))}

          {/* Ellipsis after visible pages (if needed) */}
          {visiblePages[visiblePages.length - 1] < totalPages && (
            <>
              {visiblePages[visiblePages.length - 1] < totalPages - 1 && (
                <li className="page-item disabled">
                  <span className="page-link">...</span>
                </li>
              )}
              <li className="page-item">
                <button
                  className="page-link"
                  onClick={() => onPageChange(totalPages)}
                >
                  {totalPages}
                </button>
              </li>
            </>
          )}

          {/* Next Page Button */}
          {showPrevNext && (
            <li className={`page-item ${isLastPage ? 'disabled' : ''}`}>
              <button
                className="page-link"
                onClick={() => onPageChange(currentPage + 1)}
                disabled={isLastPage}
                aria-label="Trang sau"
                title="Trang sau"
              >
                <ChevronRight size={16} />
              </button>
            </li>
          )}

          {/* Last Page Button */}
          {showFirstLast && !isLastPage && (
            <li className="page-item">
              <button
                className="page-link"
                onClick={() => onPageChange(totalPages)}
                aria-label="Trang cuối"
                title="Trang cuối"
              >
                <ChevronsRight size={16} />
              </button>
            </li>
          )}
        </ul>
      </nav>
    </div>
  );
};

export default PagingComponent;
