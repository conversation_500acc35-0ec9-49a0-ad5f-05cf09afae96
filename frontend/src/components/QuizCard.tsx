import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>ge, Spinner } from 'react-bootstrap';
import { Edit, Trash2, Play, Database, AlertTriangle, CheckCircle, History } from 'lucide-react';
import { type Quiz, type DifficultyLevel } from '../types';
import { validateQuizRules } from '../services/quizRuleService';

interface QuizCardProps {
  quiz: Quiz;
  courseTitle: string;
  isTeacherOrAdmin: boolean;
  onTakeQuiz: (quizId: number) => void;
  onEditQuiz: (quiz: Quiz) => void;
  onDeleteQuiz: (quizId: number) => void;
  onViewHistory?: (quizId: number) => void;
}

const getDifficultyColor = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case 'easy':
      return 'success';
    case 'normal':
      return 'warning';
    case 'hard':
      return 'danger';
    default:
      return 'secondary';
  }
};

const getDifficultyLabel = (difficulty: DifficultyLevel): string => {
  switch (difficulty) {
    case 'easy':
      return 'Dễ';
    case 'normal':
      return 'Trung bình';
    case 'hard':
      return 'Khó';
    default:
      return 'Không xác định';
  }
};

const QuizCard: React.FC<QuizCardProps> = ({
  quiz,
  courseTitle,
  isTeacherOrAdmin,
  onTakeQuiz,
  onEditQuiz,
  onDeleteQuiz,
  onViewHistory,
}) => {
  const [rulesValidation, setRulesValidation] = useState<{ valid: boolean; total_questions: number } | null>(null);
  const [loadingValidation, setLoadingValidation] = useState(false);

  useEffect(() => {
    if (isTeacherOrAdmin) {
      loadRulesValidation();
    }
  }, [quiz.id, isTeacherOrAdmin]);

  const loadRulesValidation = async () => {
    try {
      setLoadingValidation(true);
      const validation = await validateQuizRules(quiz.id);
      setRulesValidation({
        valid: validation.valid,
        total_questions: validation.total_questions
      });
    } catch (err) {
      // Validation might fail if no rules exist yet, which is fine
      setRulesValidation(null);
    } finally {
      setLoadingValidation(false);
    }
  };
  return (
    <Card className="h-100">
      <Card.Body>
        <div className="d-flex justify-content-between align-items-start mb-2">
          <h5 className="card-title">{quiz.title}</h5>
          <div className="d-flex gap-1 flex-wrap">
            <Badge bg={quiz.is_published ? 'success' : 'warning'}>
              {quiz.is_published ? 'Đã xuất bản' : 'Bản nháp'}
            </Badge>
            {quiz.difficulty && (
              <Badge bg={getDifficultyColor(quiz.difficulty)}>
                {getDifficultyLabel(quiz.difficulty)}
              </Badge>
            )}
            {isTeacherOrAdmin && rulesValidation && (
              <Badge
                bg={rulesValidation.valid ? 'success' : 'warning'}
                className="d-flex align-items-center"
              >
                <Database size={12} className="me-1" />
                {rulesValidation.total_questions} câu
              </Badge>
            )}
            {isTeacherOrAdmin && loadingValidation && (
              <Badge bg="secondary" className="d-flex align-items-center">
                <Spinner animation="border" size="sm" style={{ width: '12px', height: '12px' }} className="me-1" />
                Đang tải...
              </Badge>
            )}
          </div>
        </div>

        <div className="mb-2">
          <p className="text-muted small mb-1">{courseTitle}</p>
          {quiz.category ? (
            <Badge bg="info" className="small me-2">
              📚 {quiz.category.name}
            </Badge>
          ) : (
            <Badge bg="secondary" className="small me-2">
              📚 No Category
            </Badge>
          )}
        </div>

        {quiz.description && (
          <p className="card-text text-muted small mb-3">{quiz.description}</p>
        )}

        <div className="quiz-meta mb-3">
          <div className="d-flex flex-wrap gap-2">
            {/* Show total questions from quiz settings */}
            <Badge bg="info" className="small">
              📝 {quiz.total_questions || 10} câu hỏi
            </Badge>

            {/* Show rules validation for teachers/admins */}
            {isTeacherOrAdmin && rulesValidation && (
              <Badge
                bg={rulesValidation.valid ? 'success' : 'warning'}
                className="small d-flex align-items-center"
              >
                {rulesValidation.valid ? (
                  <CheckCircle size={12} className="me-1" />
                ) : (
                  <AlertTriangle size={12} className="me-1" />
                )}
                {rulesValidation.valid ? 'Rules OK' : 'Rules Issue'}
              </Badge>
            )}

            {quiz.time_limit > 0 && (
              <Badge bg="secondary" className="small">
                ⏱️ {quiz.time_limit} phút
              </Badge>
            )}
            <Badge bg="primary" className="small">
              🎯 Đạt: {quiz.pass_score}%
            </Badge>
          </div>

          {/* Show rules validation status for teachers/admins */}
          {isTeacherOrAdmin && rulesValidation && !rulesValidation.valid && (
            <div className="mt-2">
              <Badge bg="warning" className="small">
                <AlertTriangle size={12} className="me-1" />
                Cần kiểm tra quy tắc
              </Badge>
            </div>
          )}
        </div>
      </Card.Body>

      <Card.Footer className="bg-transparent">
        <div className="d-flex gap-2 flex-wrap">
          {quiz.is_published && (
            <Button
              variant="outline-primary"
              size="sm"
              onClick={() => onTakeQuiz(quiz.id)}
              disabled={Boolean(isTeacherOrAdmin && rulesValidation && !rulesValidation.valid)}
            >
              <Play size={14} className="me-1" />
              Làm bài thi
            </Button>
          )}

          {/* View History button - show for all users if quiz allows review */}
          {quiz.allow_review && onViewHistory && (
            <Button
              variant="outline-info"
              size="sm"
              onClick={() => onViewHistory(quiz.id)}
            >
              <History size={14} className="me-1" />
              Lịch sử thi
            </Button>
          )}

          {isTeacherOrAdmin && (
            <>
              <Button
                variant="outline-secondary"
                size="sm"
                onClick={() => onEditQuiz(quiz)}
              >
                <Edit size={14} className="me-1" />
                Chỉnh sửa
              </Button>
              <Button
                variant="outline-danger"
                size="sm"
                onClick={() => onDeleteQuiz(quiz.id)}
              >
                <Trash2 size={14} className="me-1" />
                Xóa
              </Button>
            </>
          )}
        </div>

        {/* Show warning for invalid rules */}
        {isTeacherOrAdmin && rulesValidation && !rulesValidation.valid && (
          <div className="mt-2">
            <small className="text-warning">
              <AlertTriangle size={12} className="me-1" />
              Quiz chưa thể sử dụng - cần cập nhật quy tắc câu hỏi
            </small>
          </div>
        )}
      </Card.Footer>
    </Card>
  );
};

export default QuizCard;
