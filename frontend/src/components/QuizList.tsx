import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, But<PERSON>, Badge } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import type { Quiz } from '../types';

interface QuizListProps {
  quizzes: Quiz[];
  courseId: number;
}

const QuizList: React.FC<QuizListProps> = ({ quizzes, courseId }) => {
  const navigate = useNavigate();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (quizzes.length === 0) return;

      switch (e.key) {
        case 'ArrowDown':
        case 'j': // Vim-style navigation
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, quizzes.length - 1));
          break;
        case 'ArrowUp':
        case 'k': // Vim-style navigation
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
        case ' ': // Spacebar
          e.preventDefault();
          if (quizzes[selectedIndex]) {
            navigate(`/quizzes/${quizzes[selectedIndex].id}/attempt`);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setSelectedIndex(0);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [quizzes, selectedIndex, navigate]);

  // Scroll selected card into view
  useEffect(() => {
    if (cardRefs.current[selectedIndex]) {
      cardRefs.current[selectedIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }, [selectedIndex]);

  if (quizzes.length === 0) {
    return (
      <Card className="text-center p-4">
        <Card.Body>
          <p className="text-muted">No quizzes available for this course yet.</p>
        </Card.Body>
      </Card>
    );
  }

  return (
    <div className="quiz-list">
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h5 className="mb-0">Available Quizzes</h5>
        <small className="text-muted">
          Use ↑↓ or j/k to navigate, Enter/Space to start quiz
        </small>
      </div>
      {quizzes.map((quiz, index) => (
        <Card
          key={quiz.id}
          ref={el => { cardRefs.current[index] = el; }}
          className={`mb-3 quiz-card ${index === selectedIndex ? 'border-primary shadow-sm' : ''}`}
          style={{
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            backgroundColor: index === selectedIndex ? '#f8f9ff' : undefined
          }}
          onClick={() => {
            setSelectedIndex(index);
            navigate(`/quizzes/${quiz.id}/attempt`);
          }}
          onMouseEnter={() => setSelectedIndex(index)}
        >
          <Card.Body>
            <div className="d-flex justify-content-between align-items-start">
              <div>
                <h5 className={index === selectedIndex ? 'text-primary' : ''}>
                  {quiz.title}
                  {index === selectedIndex && (
                    <span className="ms-2 text-primary">
                      <small>← Selected</small>
                    </span>
                  )}
                </h5>
                <p className="text-muted small mb-2">{quiz.description}</p>
                <div className="quiz-meta">
                  <Badge bg="info" className="me-2">
                    {quiz.total_questions || 0} questions
                  </Badge>
                  {quiz.time_limit > 0 && (
                    <Badge bg="secondary" className="me-2">
                      {quiz.time_limit} minutes
                    </Badge>
                  )}
                  <Badge bg="primary">
                    Pass score: {quiz.pass_score}%
                  </Badge>
                </div>
              </div>
              <Button
                variant={index === selectedIndex ? "primary" : "outline-primary"}
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(`/quizzes/${quiz.id}/attempt`);
                }}
              >
                Start Quiz
              </Button>
            </div>
          </Card.Body>
        </Card>
      ))}
    </div>
  );
};

export default QuizList;