/* Custom styles for QuizRuleForm */

.percentageSlider {
  -webkit-appearance: none;
  appearance: none;
  height: 8px;
  border-radius: 4px;
  outline: none;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  z-index: 2;
}

.percentageSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #0d6efd;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.percentageSlider::-webkit-slider-thumb:hover {
  background: #0b5ed7;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.percentageSlider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #0d6efd;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.percentageSlider::-moz-range-thumb:hover {
  background: #0b5ed7;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.percentageSlider:focus {
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

.progressContainer {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
}

.progressBar {
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
  background: #e9ecef;
}

.progressFill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 6px;
}

.progressFill.success {
  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.progressFill.warning {
  background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);
}

.progressFill.danger {
  background: linear-gradient(90deg, #dc3545 0%, #e74c3c 100%);
}

.previewCard {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.previewCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.badgeContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.percentageInput {
  border: 2px solid #dee2e6;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.percentageInput:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

/* Step marks removed for cleaner design */

.sliderLabels {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.calculatedBadge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.totalPercentageAlert {
  border-left: 4px solid #0d6efd;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
}

.totalPercentageAlert.warning {
  border-left-color: #ffc107;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.totalPercentageAlert.danger {
  border-left-color: #dc3545;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

/* Quick buttons removed for simplified design */