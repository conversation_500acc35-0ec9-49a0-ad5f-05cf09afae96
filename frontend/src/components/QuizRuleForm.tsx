import React, { useState, useEffect } from 'react';
import { Modal, Form, Button, Row, Col, Alert, Badge, ProgressBar } from 'react-bootstrap';
import {
  createQuizRule,
  updateQuizRule,
  getQuizRules,
  type CreateQuizRuleRequest,
  type UpdateQuizRuleRequest
} from '../services/quizRuleService';
import { getQuestionBankCategories } from '../services/questionBankService';
import { type QuizRule, type QuestionBankCategory, type DifficultyLevel, calculateQuestionCount } from '../types';
import styles from './QuizRuleForm.module.css';

interface QuizRuleFormProps {
  show: boolean;
  onHide: () => void;
  quizId: number;
  rule?: QuizRule | null;
  onSuccess: () => void;
  totalQuestions: number; // Add total questions to calculate counts
}

const QuizRuleForm: React.FC<QuizRuleFormProps> = ({
  show,
  onHide,
  quizId,
  rule,
  onSuccess,
  totalQuestions
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<QuestionBankCategory[]>([]);
  const [existingRules, setExistingRules] = useState<QuizRule[]>([]);

  // Form data
  const [categoryId, setCategoryId] = useState<number | ''>('');
  const [difficulty, setDifficulty] = useState<DifficultyLevel>('normal');
  const [questionType, setQuestionType] = useState<'single_choice' | 'multiple_choice'>('single_choice');
  const [percentage, setPercentage] = useState(10);

  useEffect(() => {
    if (show) {
      loadCategories();
      loadExistingRules();
      if (rule) {
        // Edit mode
        setCategoryId(rule.category_id);
        setDifficulty(rule.difficulty);
        setQuestionType(rule.question_type);
        setPercentage(rule.percentage);
      } else {
        // Create mode
        resetForm();
      }
    }
  }, [show, rule]);

  const loadCategories = async () => {
    try {
      const response = await getQuestionBankCategories();
      setCategories(response.categories);
    } catch (err) {
      setError('Failed to load categories');
      console.error('Error loading categories:', err);
    }
  };

  const loadExistingRules = async () => {
    try {
      const response = await getQuizRules(quizId);
      setExistingRules(response.rules || []);
    } catch (err) {
      console.error('Error loading existing rules:', err);
    }
  };

  const resetForm = () => {
    setCategoryId('');
    setDifficulty('normal');
    setQuestionType('single_choice');
    setPercentage(10);
    setError(null);
  };

  // Calculate current total percentage
  const getCurrentTotalPercentage = (): number => {
    const otherRules = existingRules.filter(r => rule ? r.id !== rule.id : true);
    return otherRules.reduce((sum, r) => sum + r.percentage, 0);
  };

  // Calculate remaining percentage available
  const getRemainingPercentage = (): number => {
    return 100 - getCurrentTotalPercentage();
  };

  // Calculate question count for current percentage
  const getCalculatedQuestionCount = (): number => {
    return calculateQuestionCount(percentage, totalQuestions);
  };

  const validateForm = (): string | null => {
    if (!categoryId) return 'Vui lòng chọn danh mục';
    if (percentage < 0 || percentage > 100) return 'Tỷ lệ phần trăm phải từ 0 đến 100';
    if (percentage === 0) return 'Tỷ lệ phần trăm phải lớn hơn 0';

    // Check total percentage
    const currentTotal = getCurrentTotalPercentage();
    const newTotal = currentTotal + percentage;
    if (newTotal > 100) {
      return `Tổng tỷ lệ vượt quá 100%. Hiện tại: ${currentTotal.toFixed(1)}%, còn lại: ${getRemainingPercentage().toFixed(1)}%`;
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const requestData = {
        category_id: Number(categoryId),
        difficulty,
        question_type: questionType,
        percentage
      };

      if (rule) {
        // Update existing rule
        await updateQuizRule(rule.id, requestData as UpdateQuizRuleRequest);
      } else {
        // Create new rule
        await createQuizRule(quizId, requestData as CreateQuizRuleRequest);
      }

      onSuccess();
      onHide();
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Không thể lưu quy tắc';
      setError(errorMessage);
      console.error('Error saving rule:', err);
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyLabel = (diff: DifficultyLevel) => {
    switch (diff) {
      case 'easy': return 'Dễ';
      case 'normal': return 'Trung bình';
      case 'hard': return 'Khó';
      default: return diff;
    }
  };

  const getQuestionTypeLabel = (type: string) => {
    return type === 'single_choice' ? 'Một đáp án' : 'Nhiều đáp án';
  };

  return (
    <Modal show={show} onHide={onHide} size="lg">
      <Modal.Header closeButton>
        <Modal.Title>
          {rule ? 'Chỉnh sửa quy tắc' : 'Thêm quy tắc mới'}
        </Modal.Title>
      </Modal.Header>

      <Form onSubmit={handleSubmit}>
        <Modal.Body>
          {error && (
            <Alert variant="danger" dismissible onClose={() => setError(null)}>
              {error}
            </Alert>
          )}

          <Row className="mb-3">
            <Col md={12}>
              <Form.Group>
                <Form.Label>Danh mục *</Form.Label>
                <Form.Select
                  value={categoryId}
                  onChange={(e) => setCategoryId(e.target.value as number | '')}
                  required
                >
                  <option value="">Chọn danh mục</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={4}>
              <Form.Group>
                <Form.Label>Độ khó</Form.Label>
                <Form.Select
                  value={difficulty}
                  onChange={(e) => setDifficulty(e.target.value as DifficultyLevel)}
                >
                  <option value="easy">Dễ</option>
                  <option value="normal">Trung bình</option>
                  <option value="hard">Khó</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>Loại câu hỏi</Form.Label>
                <Form.Select
                  value={questionType}
                  onChange={(e) => setQuestionType(e.target.value as 'single_choice' | 'multiple_choice')}
                >
                  <option value="single_choice">Một đáp án</option>
                  <option value="multiple_choice">Nhiều đáp án</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group>
                <Form.Label>
                  Tỷ lệ phần trăm *
                  <Badge bg="primary" className="ms-2">
                    {percentage.toFixed(1)}% = {getCalculatedQuestionCount()} câu
                  </Badge>
                </Form.Label>
                <Form.Range
                  min={0}
                  max={getRemainingPercentage() + (rule ? rule.percentage : 0)}
                  step={5}
                  value={percentage}
                  onChange={(e) => setPercentage(Number(e.target.value))}
                  className={`${styles.percentageSlider}`}
                  style={{
                    background: `linear-gradient(to right, #0d6efd 0%, #0d6efd ${(percentage / (getRemainingPercentage() + (rule ? rule.percentage : 0))) * 100}%, #dee2e6 ${(percentage / (getRemainingPercentage() + (rule ? rule.percentage : 0))) * 100}%, #dee2e6 100%)`
                  }}
                />
              </Form.Group>
            </Col>
          </Row>

          {/* Percentage Summary */}
          <Row className="mb-3">
            <Col md={12}>
              <div className={`${styles.progressContainer} ${getCurrentTotalPercentage() + percentage > 100 ? styles.totalPercentageAlert + ' ' + styles.danger : styles.totalPercentageAlert}`}>
                <h6 className="mb-3">Tổng quan tỷ lệ phần trăm</h6>
                <div className="mb-2">
                  <div className="d-flex justify-content-between align-items-center mb-1">
                    <span>Tỷ lệ đã sử dụng:</span>
                    <Badge bg={getCurrentTotalPercentage() + percentage > 100 ? 'danger' : 'success'} className={styles.calculatedBadge}>
                      {(getCurrentTotalPercentage() + percentage).toFixed(1)}%
                    </Badge>
                  </div>
                  <div className={styles.progressBar}>
                    <div
                      className={`${styles.progressFill} ${getCurrentTotalPercentage() + percentage > 100 ? styles.danger : styles.success}`}
                      style={{ width: `${Math.min(getCurrentTotalPercentage() + percentage, 100)}%` }}
                    />
                  </div>
                  <div className="d-flex justify-content-between small text-muted mt-2">
                    <span>Đã có: {getCurrentTotalPercentage().toFixed(1)}%</span>
                    <span>Hiện tại: +{percentage.toFixed(1)}%</span>
                    <span>Còn lại: {Math.max(0, getRemainingPercentage() - percentage).toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            </Col>
          </Row>

          <Row className="mb-3">
            <Col md={12}>
              <div className="w-100">
                <h6>Xem trước quy tắc:</h6>
                <div className={styles.previewCard}>
                  <div className={styles.badgeContainer}>
                    <Badge bg="primary">{categories.find(c => c.id === Number(categoryId))?.name || 'Chưa chọn'}</Badge>
                    <Badge bg="warning">{getDifficultyLabel(difficulty)}</Badge>
                    <Badge bg="info">{getQuestionTypeLabel(questionType)}</Badge>
                  </div>
                  <div className="small">
                    <strong>Tỷ lệ:</strong> {percentage.toFixed(1)}% của tổng số câu hỏi
                    <br />
                    <strong>Số câu hỏi dự kiến:</strong> <Badge bg="info" className={styles.calculatedBadge}>{getCalculatedQuestionCount()} câu</Badge>
                  </div>
                </div>
              </div>
            </Col>
          </Row>

          <Alert variant="info">
            <h6>Lưu ý:</h6>
            <ul className="mb-0">
              <li>Tổng tỷ lệ của tất cả quy tắc phải bằng 100%</li>
              <li>Hệ thống sẽ chọn ngẫu nhiên câu hỏi phù hợp với điều kiện</li>
              <li>Đảm bảo ngân hàng câu hỏi có đủ câu hỏi theo điều kiện</li>
              <li>Ví dụ: Quiz có {totalQuestions} câu, quy tắc {percentage.toFixed(1)}% sẽ tạo {getCalculatedQuestionCount()} câu hỏi</li>
            </ul>
          </Alert>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={onHide} disabled={loading}>
            Hủy
          </Button>
          <Button variant="primary" type="submit" disabled={loading}>
            {loading ? 'Đang lưu...' : (rule ? 'Cập nhật' : 'Tạo mới')}
          </Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
};

export default QuizRuleForm;
