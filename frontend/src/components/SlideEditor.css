/* Slide Editor Styles */
.slide-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.slide-editor .markdown-body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
}

.slide-editor .markdown-body h1 {
  border-bottom: 1px solid #d0d7de;
  padding-bottom: 0.3em;
  margin-bottom: 16px;
}

.slide-editor .markdown-body h2 {
  border-bottom: 1px solid #d0d7de;
  padding-bottom: 0.3em;
  margin-bottom: 16px;
}

.slide-editor .markdown-body blockquote {
  padding: 0 1em;
  color: #656d76;
  border-left: 0.25em solid #d0d7de;
  margin: 0 0 16px 0;
}

.slide-editor .markdown-body table {
  border-spacing: 0;
  border-collapse: collapse;
  display: block;
  width: max-content;
  max-width: 100%;
  overflow: auto;
}

.slide-editor .markdown-body table th,
.slide-editor .markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #d0d7de;
}

.slide-editor .markdown-body table th {
  font-weight: 600;
  background-color: #f6f8fa;
}

.slide-editor .markdown-body table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.slide-editor .markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.slide-editor .markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(175, 184, 193, 0.2);
  border-radius: 6px;
}

.slide-editor .markdown-body pre code {
  background-color: transparent;
  padding: 0;
  margin: 0;
  font-size: 100%;
}

/* Editor specific styles */
.slide-editor textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
  tab-size: 2;
}

.slide-editor textarea:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  border-color: #80bdff;
}

/* Preview specific styles */
.slide-editor .preview-container {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

.slide-editor .preview-container .markdown-body {
  padding: 1rem;
  min-height: 200px;
}

/* Button group styles */
.slide-editor .btn-group .btn {
  border-radius: 0;
}

.slide-editor .btn-group .btn:first-child {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.slide-editor .btn-group .btn:last-child {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .slide-editor .btn-group {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .slide-editor .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }
  
  .slide-editor .btn-group .btn:last-child {
    margin-bottom: 0;
  }
}

/* Loading and error states */
.slide-editor .loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* Slide title preview */
.slide-editor .slide-title-preview {
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
}

/* Custom scrollbar for preview */
.slide-editor .preview-container::-webkit-scrollbar {
  width: 8px;
}

.slide-editor .preview-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.slide-editor .preview-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.slide-editor .preview-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Syntax highlighting for code blocks */
.slide-editor .markdown-body pre[class*="language-"] {
  background-color: #f6f8fa;
  border: 1px solid #d0d7de;
}

.slide-editor .markdown-body code[class*="language-"] {
  background-color: transparent;
}

/* Image styles in preview */
.slide-editor .markdown-body img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

/* List styles */
.slide-editor .markdown-body ul,
.slide-editor .markdown-body ol {
  padding-left: 2em;
  margin-bottom: 16px;
}

.slide-editor .markdown-body li {
  margin-bottom: 0.25em;
}

/* Link styles */
.slide-editor .markdown-body a {
  color: #0969da;
  text-decoration: none;
}

.slide-editor .markdown-body a:hover {
  text-decoration: underline;
}

/* Task list styles */
.slide-editor .markdown-body .task-list-item {
  list-style-type: none;
  margin-left: -1.5em;
}

.slide-editor .markdown-body .task-list-item input[type="checkbox"] {
  margin-right: 0.5em;
}
