import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';
import { Save, Eye, Edit3 } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import 'github-markdown-css/github-markdown-light.css';
import './SlideEditor.css';

interface SlideEditorProps {
  initialContent?: string;
  initialTitle?: string;
  onSave?: (title: string, content: string) => void;
  onCancel?: () => void;
  loading?: boolean;
  error?: string | null;
}

const SlideEditor: React.FC<SlideEditorProps> = ({
  initialContent = '',
  initialTitle = '',
  onSave,
  onCancel,
  loading = false,
  error = null,
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [previewMode, setPreviewMode] = useState<'split' | 'preview' | 'edit'>('split');

  // Update content when initialContent changes
  useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  useEffect(() => {
    setTitle(initialTitle);
  }, [initialTitle]);

  const handleSave = () => {
    if (onSave) {
      onSave(title, content);
    }
  };

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setContent(e.target.value);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  return (
    <Container fluid className="h-100 slide-editor">
      {error && (
        <Alert variant="danger" className="mb-3">
          {error}
        </Alert>
      )}

      {/* Header with title and controls */}
      <Row className="mb-3">
        <Col>
          <Card>
            <Card.Body>
              <Row className="align-items-center">
                <Col md={6}>
                  <Form.Group>
                    <Form.Label>Slide Title</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="Enter slide title..."
                      value={title}
                      onChange={handleTitleChange}
                    />
                  </Form.Group>
                </Col>
                <Col md={6} className="text-end">
                  <div className="btn-group me-3" role="group">
                    <Button
                      variant={previewMode === 'edit' ? 'primary' : 'outline-primary'}
                      size="sm"
                      onClick={() => setPreviewMode('edit')}
                    >
                      <Edit3 size={16} className="me-1" />
                      Edit
                    </Button>
                    <Button
                      variant={previewMode === 'split' ? 'primary' : 'outline-primary'}
                      size="sm"
                      onClick={() => setPreviewMode('split')}
                    >
                      Split
                    </Button>
                    <Button
                      variant={previewMode === 'preview' ? 'primary' : 'outline-primary'}
                      size="sm"
                      onClick={() => setPreviewMode('preview')}
                    >
                      <Eye size={16} className="me-1" />
                      Preview
                    </Button>
                  </div>
                  <Button
                    variant="success"
                    onClick={handleSave}
                    disabled={loading || !title.trim() || !content.trim()}
                    className="me-2"
                  >
                    <Save size={16} className="me-1" />
                    {loading ? 'Saving...' : 'Save Slide'}
                  </Button>
                  {onCancel && (
                    <Button variant="outline-secondary" onClick={onCancel}>
                      Cancel
                    </Button>
                  )}
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Editor and Preview */}
      <Row className="flex-grow-1" style={{ height: 'calc(100vh - 200px)' }}>
        {/* Editor Column */}
        {(previewMode === 'edit' || previewMode === 'split') && (
          <Col md={previewMode === 'split' ? 6 : 12} className="h-100">
            <Card className="h-100">
              <Card.Header>
                <h6 className="mb-0">
                  <Edit3 size={16} className="me-2" />
                  Markdown Editor
                </h6>
              </Card.Header>
              <Card.Body className="p-0 h-100">
                <Form.Control
                  as="textarea"
                  value={content}
                  onChange={handleContentChange}
                  placeholder="Enter your slide content in Markdown format...

# Example Slide

## Introduction
This is a sample slide content written in **Markdown**.

### Features
- Support for **bold** and *italic* text
- Lists and numbered lists
- Code blocks
- Links and images
- Tables

```javascript
// Code example
function hello() {
  console.log('Hello, World!');
}
```

> This is a blockquote

| Column 1 | Column 2 |
|----------|----------|
| Data 1   | Data 2   |
"
                  className="border-0 h-100"
                  style={{
                    resize: 'none',
                    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                    fontSize: '14px',
                    lineHeight: '1.5',
                  }}
                />
              </Card.Body>
            </Card>
          </Col>
        )}

        {/* Preview Column */}
        {(previewMode === 'preview' || previewMode === 'split') && (
          <Col md={previewMode === 'split' ? 6 : 12} className="h-100">
            <Card className="h-100">
              <Card.Header>
                <h6 className="mb-0">
                  <Eye size={16} className="me-2" />
                  Live Preview
                </h6>
              </Card.Header>
              <Card.Body className="h-100 overflow-auto">
                {title && (
                  <h2 className="text-center mb-4 pb-3 border-bottom">
                    {title}
                  </h2>
                )}
                <div className="markdown-body">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeRaw]}
                  >
                    {content || '*No content yet. Start typing in the editor to see the preview.*'}
                  </ReactMarkdown>
                </div>
              </Card.Body>
            </Card>
          </Col>
        )}
      </Row>
    </Container>
  );
};

export default SlideEditor;
