import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, <PERSON>, <PERSON>, Badge, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Nav } from 'react-bootstrap';
import { BarChart3, Users, BookOpen, TrendingUp, Eye, Filter } from 'lucide-react';
import { getAllQuizzesSummary, getQuizStats, getQuizAttemptsList } from '../services/quizHistoryService';
import { getUsers } from '../services/userService';
import type { AllQuizzesSummary, QuizStats, QuizAttemptsList, User } from '../types';

const AdminDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'quizzes' | 'users'>('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Overview data
  const [quizzesSummary, setQuizzesSummary] = useState<AllQuizzesSummary | null>(null);
  const [users, setUsers] = useState<User[]>([]);

  // Selected quiz details
  const [selectedQuizStats, setSelectedQuizStats] = useState<QuizStats | null>(null);
  const [selectedQuizAttempts, setSelectedQuizAttempts] = useState<QuizAttemptsList | null>(null);

  useEffect(() => {
    loadOverviewData();
  }, []);

  const loadOverviewData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [summaryData, usersData] = await Promise.all([
        getAllQuizzesSummary(),
        getUsers()
      ]);

      setQuizzesSummary(summaryData);
      setUsers(usersData);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const loadQuizDetails = async (quizId: number) => {
    try {
      const [statsData, attemptsData] = await Promise.all([
        getQuizStats(quizId),
        getQuizAttemptsList(quizId, 1, 10)
      ]);

      setSelectedQuizStats(statsData);
      setSelectedQuizAttempts(attemptsData);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load quiz details');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getPassRateColor = (passRate: number) => {
    if (passRate >= 80) return 'success';
    if (passRate >= 60) return 'warning';
    return 'danger';
  };

  if (loading) {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <Spinner animation="border" />
          <p className="mt-2">Đang tải dashboard...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>
          <BarChart3 className="me-2" />
          Admin Dashboard
        </h1>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
          <Button variant="link" className="p-0 ms-2" onClick={() => setError(null)}>
            Dismiss
          </Button>
        </Alert>
      )}

      {/* Navigation Tabs */}
      <Nav variant="tabs" className="mb-4">
        <Nav.Item>
          <Nav.Link
            active={activeTab === 'overview'}
            onClick={() => setActiveTab('overview')}
          >
            <TrendingUp size={16} className="me-1" />
            Tổng quan
          </Nav.Link>
        </Nav.Item>
        <Nav.Item>
          <Nav.Link
            active={activeTab === 'quizzes'}
            onClick={() => setActiveTab('quizzes')}
          >
            <BookOpen size={16} className="me-1" />
            Quizzes
          </Nav.Link>
        </Nav.Item>
        <Nav.Item>
          <Nav.Link
            active={activeTab === 'users'}
            onClick={() => setActiveTab('users')}
          >
            <Users size={16} className="me-1" />
            Users
          </Nav.Link>
        </Nav.Item>
      </Nav>

      {/* Overview Tab */}
      {activeTab === 'overview' && quizzesSummary && (
        <Row>
          <Col lg={8}>
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">Thống kê tổng quan</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={3}>
                    <div className="text-center p-3">
                      <BookOpen size={32} className="text-primary mb-2" />
                      <h4>{quizzesSummary.quizzes.length}</h4>
                      <small className="text-muted">Tổng Quiz</small>
                    </div>
                  </Col>
                  <Col md={3}>
                    <div className="text-center p-3">
                      <Users size={32} className="text-success mb-2" />
                      <h4>{users.length}</h4>
                      <small className="text-muted">Tổng Users</small>
                    </div>
                  </Col>
                  <Col md={3}>
                    <div className="text-center p-3">
                      <TrendingUp size={32} className="text-info mb-2" />
                      <h4>
                        {quizzesSummary.quizzes.reduce((sum, q) => sum + q.total_attempts, 0)}
                      </h4>
                      <small className="text-muted">Tổng lượt thi</small>
                    </div>
                  </Col>
                  <Col md={3}>
                    <div className="text-center p-3">
                      <BarChart3 size={32} className="text-warning mb-2" />
                      <h4>
                        {quizzesSummary.quizzes.reduce((sum, q) => sum + q.unique_users, 0)}
                      </h4>
                      <small className="text-muted">Users đã thi</small>
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            <Card>
              <Card.Header>
                <h5 className="mb-0">Top Quizzes theo lượt thi</h5>
              </Card.Header>
              <Card.Body>
                <Table responsive>
                  <thead>
                    <tr>
                      <th>Quiz</th>
                      <th>Lượt thi</th>
                      <th>Pass Rate</th>
                      <th>Điểm TB</th>
                    </tr>
                  </thead>
                  <tbody>
                    {quizzesSummary.quizzes
                      .sort((a, b) => b.total_attempts - a.total_attempts)
                      .slice(0, 10)
                      .map((quiz) => (
                        <tr key={quiz.quiz.id}>
                          <td>{quiz.quiz.title}</td>
                          <td>
                            <Badge bg="primary">{quiz.total_attempts}</Badge>
                          </td>
                          <td>
                            <Badge bg={getPassRateColor(quiz.pass_rate)}>
                              {quiz.pass_rate.toFixed(1)}%
                            </Badge>
                          </td>
                          <td>{quiz.average_score.toFixed(1)}</td>
                        </tr>
                      ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={4}>
            <Card className="mb-4">
              <Card.Header>
                <h6 className="mb-0">Phân bố Users theo Role</h6>
              </Card.Header>
              <Card.Body>
                {['admin', 'teacher', 'student'].map(role => {
                  const count = users.filter(u => u.role === role).length;
                  const percentage = users.length > 0 ? (count / users.length * 100) : 0;
                  return (
                    <div key={role} className="mb-3">
                      <div className="d-flex justify-content-between">
                        <span className="text-capitalize">{role}</span>
                        <span>{count}</span>
                      </div>
                      <div className="progress" style={{ height: '8px' }}>
                        <div
                          className={`progress-bar bg-${role === 'admin' ? 'danger' : role === 'teacher' ? 'warning' : 'info'}`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </Card.Body>
            </Card>

            <Card>
              <Card.Header>
                <h6 className="mb-0">Quizzes theo Pass Rate</h6>
              </Card.Header>
              <Card.Body>
                <div className="small">
                  <div className="d-flex justify-content-between mb-2">
                    <span>Cao (≥80%)</span>
                    <Badge bg="success">
                      {quizzesSummary.quizzes.filter(q => q.pass_rate >= 80).length}
                    </Badge>
                  </div>
                  <div className="d-flex justify-content-between mb-2">
                    <span>Trung bình (60-79%)</span>
                    <Badge bg="warning">
                      {quizzesSummary.quizzes.filter(q => q.pass_rate >= 60 && q.pass_rate < 80).length}
                    </Badge>
                  </div>
                  <div className="d-flex justify-content-between">
                    <span>Thấp (&lt;60%)</span>
                    <Badge bg="danger">
                      {quizzesSummary.quizzes.filter(q => q.pass_rate < 60).length}
                    </Badge>
                  </div>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Quizzes Tab */}
      {activeTab === 'quizzes' && quizzesSummary && (
        <Card>
          <Card.Header>
            <h5 className="mb-0">Chi tiết Quizzes</h5>
          </Card.Header>
          <Card.Body>
            <Table responsive hover>
              <thead>
                <tr>
                  <th>Quiz</th>
                  <th>Lượt thi</th>
                  <th>Users</th>
                  <th>Pass Rate</th>
                  <th>Điểm TB</th>
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {quizzesSummary.quizzes.map((quiz) => (
                  <tr key={quiz.quiz.id}>
                    <td>
                      <div>
                        <strong>{quiz.quiz.title}</strong>
                        <br />
                        <small className="text-muted">
                          {quiz.quiz.total_questions} câu • {quiz.quiz.time_limit || 'Không giới hạn'} phút
                        </small>
                      </div>
                    </td>
                    <td>
                      <Badge bg="primary">{quiz.total_attempts}</Badge>
                    </td>
                    <td>
                      <Badge bg="info">{quiz.unique_users}</Badge>
                    </td>
                    <td>
                      <Badge bg={getPassRateColor(quiz.pass_rate)}>
                        {quiz.pass_rate.toFixed(1)}%
                      </Badge>
                    </td>
                    <td>{quiz.average_score.toFixed(1)}</td>
                    <td>
                      <Button
                        variant="outline-primary"
                        size="sm"
                        onClick={() => loadQuizDetails(quiz.quiz.id)}
                      >
                        <Eye size={14} className="me-1" />
                        Chi tiết
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Card.Body>
        </Card>
      )}

      {/* Users Tab */}
      {activeTab === 'users' && (
        <Card>
          <Card.Header>
            <h5 className="mb-0">Quản lý Users</h5>
          </Card.Header>
          <Card.Body>
            <Table responsive hover>
              <thead>
                <tr>
                  <th>User</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Trạng thái</th>
                  <th>Ngày tạo</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id}>
                    <td>
                      <strong>{user.first_name} {user.last_name}</strong>
                    </td>
                    <td>{user.email}</td>
                    <td>
                      <Badge bg={
                        user.role === 'admin' ? 'danger' :
                          user.role === 'teacher' ? 'warning' : 'info'
                      }>
                        {user.role}
                      </Badge>
                    </td>
                    <td>
                      <Badge bg={user.is_active ? 'success' : 'secondary'}>
                        {user.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td>{formatDate(user.created_at)}</td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Card.Body>
        </Card>
      )}

      {/* Quiz Details Modal/Section */}
      {selectedQuizStats && (
        <Card className="mt-4">
          <Card.Header>
            <h5 className="mb-0">Chi tiết: {selectedQuizStats.quiz.title}</h5>
          </Card.Header>
          <Card.Body>
            <Row className="mb-4">
              <Col md={3}>
                <div className="text-center p-3 border rounded">
                  <h4 className="text-primary">{selectedQuizStats.stats.total_attempts}</h4>
                  <small>Tổng lượt thi</small>
                </div>
              </Col>
              <Col md={3}>
                <div className="text-center p-3 border rounded">
                  <h4 className="text-success">{selectedQuizStats.stats.passed_attempts}</h4>
                  <small>Đạt</small>
                </div>
              </Col>
              <Col md={3}>
                <div className="text-center p-3 border rounded">
                  <h4 className="text-danger">{selectedQuizStats.stats.failed_attempts}</h4>
                  <small>Không đạt</small>
                </div>
              </Col>
              <Col md={3}>
                <div className="text-center p-3 border rounded">
                  <h4 className="text-info">{selectedQuizStats.stats.unique_users}</h4>
                  <small>Users tham gia</small>
                </div>
              </Col>
            </Row>

            {selectedQuizAttempts && (
              <div>
                <h6>Lượt thi gần đây</h6>
                <Table responsive>
                  <thead>
                    <tr>
                      <th>User</th>
                      <th>Điểm</th>
                      <th>Tỷ lệ</th>
                      <th>Trạng thái</th>
                      <th>Thời gian</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedQuizAttempts.attempts.map((attempt) => (
                      <tr key={attempt.id}>
                        <td>{attempt.user?.first_name} {attempt.user?.last_name}</td>
                        <td>{attempt.score.toFixed(1)}/{attempt.max_score.toFixed(1)}</td>
                        <td>
                          <Badge bg={attempt.is_passed ? 'success' : 'danger'}>
                            {attempt.percentage.toFixed(1)}%
                          </Badge>
                        </td>
                        <td>
                          <Badge bg={attempt.is_passed ? 'success' : 'danger'}>
                            {attempt.is_passed ? 'Đạt' : 'Không đạt'}
                          </Badge>
                        </td>
                        <td>{formatDate(attempt.completed_at)}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
            )}
          </Card.Body>
        </Card>
      )}
    </Container>
  );
};

export default AdminDashboard;
