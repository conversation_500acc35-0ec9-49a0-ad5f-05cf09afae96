import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, Form, But<PERSON>, Alert, Spinner } from 'react-bootstrap';
import { ArrowLeft, Save, BookOpen, Image, FileText } from 'lucide-react';
import { courseService } from '../services/courseService';

const CourseCreate: React.FC = () => {
  const navigate = useNavigate();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    thumbnail: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      setError('Course title is required');
      return;
    }

    try {
      setSaving(true);
      setError(null);
      
      const newCourse = await courseService.createCourse({
        title: formData.title.trim(),
        description: formData.description.trim(),
        thumbnail: formData.thumbnail.trim() || undefined
      });

      // Navigate to the new course edit page
      navigate(`/courses/${newCourse.id}/edit`);
    } catch (err: any) {
      console.error('Error creating course:', err);
      setError(err.response?.data?.error || 'Failed to create course');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/courses');
  };

  return (
    <Container className="py-4">
      <div className="d-flex align-items-center mb-4">
        <Button
          variant="outline-secondary"
          onClick={handleCancel}
          className="me-3"
        >
          <ArrowLeft size={16} className="me-1" />
          Back to Courses
        </Button>
        <h2 className="mb-0">Create New Course</h2>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4" dismissible onClose={() => setError(null)}>
          <strong>Error:</strong> {error}
        </Alert>
      )}

      <Row>
        <Col lg={8}>
          <Card className="shadow-sm">
            <Card.Header className="bg-primary text-white">
              <h5 className="mb-0 d-flex align-items-center">
                <BookOpen size={20} className="me-2" />
                Course Information
              </h5>
            </Card.Header>
            <Card.Body>
              <Form onSubmit={handleSubmit}>
                {/* Course Title */}
                <Form.Group className="mb-4">
                  <Form.Label className="fw-semibold">
                    <FileText size={16} className="me-2" />
                    Course Title *
                  </Form.Label>
                  <Form.Control
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter course title..."
                    required
                    className="form-control-lg"
                  />
                  <Form.Text className="text-muted">
                    Choose a clear, descriptive title for your course
                  </Form.Text>
                </Form.Group>

                {/* Course Description */}
                <Form.Group className="mb-4">
                  <Form.Label className="fw-semibold">
                    Course Description
                  </Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={6}
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Describe what students will learn in this course..."
                    style={{ resize: 'vertical' }}
                  />
                  <Form.Text className="text-muted">
                    Provide a detailed description of the course content and learning objectives
                  </Form.Text>
                </Form.Group>

                {/* Thumbnail URL */}
                <Form.Group className="mb-4">
                  <Form.Label className="fw-semibold">
                    <Image size={16} className="me-2" />
                    Thumbnail Image URL
                  </Form.Label>
                  <Form.Control
                    type="url"
                    value={formData.thumbnail}
                    onChange={(e) => handleInputChange('thumbnail', e.target.value)}
                    placeholder="https://example.com/image.jpg"
                  />
                  <Form.Text className="text-muted">
                    Optional: Add a thumbnail image URL for your course
                  </Form.Text>
                </Form.Group>

                {/* Action Buttons */}
                <div className="d-flex justify-content-end gap-2">
                  <Button
                    variant="outline-secondary"
                    onClick={handleCancel}
                    disabled={saving}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    variant="primary"
                    disabled={saving || !formData.title.trim()}
                  >
                    {saving ? (
                      <>
                        <Spinner as="span" animation="border" size="sm" className="me-2" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save size={16} className="me-2" />
                        Create Course
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>

        <Col lg={4}>
          {/* Preview Card */}
          <Card className="shadow-sm mb-4">
            <Card.Header>
              <h6 className="mb-0">Course Preview</h6>
            </Card.Header>
            <Card.Body>
              {formData.thumbnail && (
                <div className="mb-3">
                  <img
                    src={formData.thumbnail}
                    alt="Course thumbnail"
                    className="img-fluid rounded"
                    style={{ width: '100%', height: '150px', objectFit: 'cover' }}
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}
              <h6 className="fw-bold">
                {formData.title || 'Course Title'}
              </h6>
              <p className="text-muted small mb-0">
                {formData.description || 'Course description will appear here...'}
              </p>
            </Card.Body>
          </Card>

          {/* Tips Card */}
          <Card className="shadow-sm">
            <Card.Header>
              <h6 className="mb-0">💡 Tips for Creating Great Courses</h6>
            </Card.Header>
            <Card.Body>
              <ul className="small mb-0">
                <li className="mb-2">Use a clear, specific title that describes what students will learn</li>
                <li className="mb-2">Write a compelling description that highlights key benefits</li>
                <li className="mb-2">Choose a high-quality thumbnail image (recommended: 1280x720px)</li>
                <li className="mb-0">You can always edit these details later</li>
              </ul>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default CourseCreate;
