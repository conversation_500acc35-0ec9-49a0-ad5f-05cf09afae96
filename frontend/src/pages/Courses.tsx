import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Button, Row, Col, Spinner, Alert, Modal } from 'react-bootstrap';
import { Plus, BookOpen, RefreshCw, Trash2 } from 'lucide-react';
import { useCourses } from '../hooks/useCourses';
import { useAuth } from '../hooks/useAuth';
import CourseCard from '../components/CourseCard';
import { courseService } from '../services/courseService';

const Courses: React.FC = () => {
  const navigate = useNavigate();
  const { courses, loading, error, refetch } = useCourses();
  const { state } = useAuth();
  const isTeacherOrAdmin = state.user?.role === 'teacher' || state.user?.role === 'admin';

  // Delete confirmation modal state
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [courseToDelete, setCourseToDelete] = React.useState<any>(null);
  const [deleting, setDeleting] = React.useState(false);
  const [deleteError, setDeleteError] = React.useState<string | null>(null);

  const handleViewCourse = (courseId: number) => {
    navigate(`/courses/${courseId}`);
  };

  const handleEditCourse = (course: any) => {
    navigate(`/courses/${course.id}/edit`);
  };

  const handleDeleteCourse = (courseId: number) => {
    const course = courses.find(c => c.id === courseId);
    if (course) {
      setCourseToDelete(course);
      setShowDeleteModal(true);
      setDeleteError(null);
    }
  };

  const handleConfirmDelete = async () => {
    if (!courseToDelete) return;

    try {
      setDeleting(true);
      setDeleteError(null);

      await courseService.deleteCourse(courseToDelete.id);

      // Close modal and refresh courses
      setShowDeleteModal(false);
      setCourseToDelete(null);
      await refetch();
    } catch (err: any) {
      console.error('Error deleting course:', err);
      setDeleteError(err.response?.data?.error || 'Failed to delete course');
    } finally {
      setDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setCourseToDelete(null);
    setDeleteError(null);
  };

  const handleAddCourse = () => {
    navigate('/courses/create');
  };

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>Courses</h2>
        <div className="d-flex gap-2">
          <Button
            variant="outline-secondary"
            onClick={refetch}
            disabled={loading}
          >
            <RefreshCw size={16} className={`me-2 ${loading ? 'spin' : ''}`} />
            Refresh
          </Button>
          {isTeacherOrAdmin && (
            <Button variant="primary" onClick={handleAddCourse}>
              <Plus size={16} className="me-2" />
              Add Course
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <strong>Error:</strong> {error}
          <Button
            variant="link"
            className="p-0 ms-2"
            onClick={refetch}
          >
            Try again
          </Button>
        </Alert>
      )}

      {loading ? (
        <div className="text-center py-5">
          <Spinner animation="border" role="status" className="mb-3">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="text-muted">Loading courses...</p>
        </div>
      ) : courses.length === 0 ? (
        <Row>
          <Col lg={8}>
            <Card>
              <Card.Body className="text-center py-5">
                <BookOpen size={64} className="text-muted mb-3" />
                <h4>No Courses Found</h4>
                <p className="text-muted">
                  {isTeacherOrAdmin
                    ? "Get started by creating your first course!"
                    : "No courses are available at the moment."
                  }
                </p>
                {isTeacherOrAdmin && (
                  <Button variant="primary" onClick={handleAddCourse}>
                    <Plus size={16} className="me-2" />
                    Create Your First Course
                  </Button>
                )}
              </Card.Body>
            </Card>
          </Col>
          <Col lg={4}>
            <Card>
              <Card.Header>
                <h6 className="mb-0">Quick Actions</h6>
              </Card.Header>
              <Card.Body>
                <div className="d-grid gap-2">
                  {isTeacherOrAdmin && (
                    <>
                      <Button variant="outline-primary" size="sm">
                        Import Course
                      </Button>
                      <Button variant="outline-info" size="sm">
                        Course Templates
                      </Button>
                    </>
                  )}
                  <Button variant="outline-secondary" size="sm">
                    Export Data
                  </Button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      ) : (
        <Row>
          {courses.map((course) => (
            <Col key={course.id} lg={4} md={6} className="mb-4">
              <CourseCard
                course={course}
                onView={handleViewCourse}
                onEdit={isTeacherOrAdmin ? handleEditCourse : undefined}
                onDelete={isTeacherOrAdmin ? handleDeleteCourse : undefined}
              />
            </Col>
          ))}
        </Row>
      )}

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={handleCancelDelete} centered>
        <Modal.Header closeButton className="bg-danger text-white">
          <Modal.Title className="d-flex align-items-center">
            <Trash2 size={20} className="me-2" />
            Delete Course
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {deleteError && (
            <Alert variant="danger" className="mb-3">
              <strong>Error:</strong> {deleteError}
            </Alert>
          )}

          <div className="text-center">
            <div className="mb-3">
              <Trash2 size={48} className="text-danger opacity-50" />
            </div>
            <h5 className="mb-3">Are you sure you want to delete this course?</h5>
            {courseToDelete && (
              <div className="bg-light p-3 rounded mb-3">
                <h6 className="fw-bold mb-1">{courseToDelete.title}</h6>
                <p className="text-muted small mb-0">
                  {courseToDelete.description || 'No description'}
                </p>
              </div>
            )}
            <p className="text-muted mb-0">
              <strong>Warning:</strong> This action cannot be undone. All lessons, slides, and related data will be permanently deleted.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="outline-secondary" onClick={handleCancelDelete} disabled={deleting}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleConfirmDelete} disabled={deleting}>
            {deleting ? (
              <>
                <Spinner as="span" animation="border" size="sm" className="me-2" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 size={16} className="me-2" />
                Delete Course
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default Courses;
