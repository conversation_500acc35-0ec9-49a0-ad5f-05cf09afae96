import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Alert, Button } from 'react-bootstrap';
import { ArrowLeft } from 'lucide-react';
import { useSlides, useLesson } from '../hooks/useSlides';
import { SlideViewer } from '../components';
import { type Slide } from '../types';

const LessonViewer: React.FC = () => {
  const { lessonId } = useParams<{ lessonId: string }>();
  const navigate = useNavigate();

  const lessonIdNum = lessonId ? parseInt(lessonId, 10) : 0;
  const { lesson, loading: lessonLoading, error: lessonError } = useLesson(lessonIdNum);
  const { slides, loading: slidesLoading, error: slidesError, markSlideComplete } = useSlides(lessonIdNum);

  const handleSlideChange = (slideIndex: number, slide: Slide) => {
    console.log(`Viewing slide ${slideIndex + 1}: ${slide.title || 'Untitled'}`);
  };

  const handleSlideComplete = async (slideId: number) => {
    try {
      await markSlideComplete(slideId);
      console.log(`Slide ${slideId} marked as complete`);
    } catch (error) {
      console.error('Failed to mark slide as complete:', error);
    }
  };

  const handleExit = () => {
    if (lesson?.course?.id) {
      navigate(`/courses/${lesson.course.id}`);
    } else {
      navigate('/courses');
    }
  };

  if (!lessonId || isNaN(lessonIdNum)) {
    return (
      <Container>
        <Alert variant="danger">
          <strong>Invalid lesson ID</strong>
          <div className="mt-2">
            <Button variant="outline-secondary" onClick={() => navigate('/courses')}>
              <ArrowLeft size={16} className="me-1" />
              Back to Courses
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  if (lessonError || slidesError) {
    return (
      <Container>
        <Alert variant="danger">
          <strong>Error loading lesson:</strong> {lessonError || slidesError}
          <div className="mt-2">
            <Button variant="outline-secondary" onClick={handleExit}>
              <ArrowLeft size={16} className="me-1" />
              Back to Course
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  return (
    <div className="lesson-viewer-page">
      <SlideViewer
        slides={slides}
        lessonTitle={lesson?.title || 'Loading...'}
        onSlideChange={handleSlideChange}
        onSlideComplete={handleSlideComplete}
        onExit={handleExit}
        loading={lessonLoading || slidesLoading}
        error={lessonError || slidesError}
      />
    </div>
  );
};

export default LessonViewer;
