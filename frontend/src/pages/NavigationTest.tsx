import React from 'react';
import { useNavigate, useSearchParams, useParams } from 'react-router-dom';
import { Contain<PERSON>, <PERSON><PERSON>, Card, Alert } from 'react-bootstrap';

const NavigationTest: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const params = useParams();

  const testNavigation = () => {
    const courseId = '1'; // Test với course ID 1
    const lessonId = '1'; // Test với lesson ID 1

    console.log('Testing navigation to SlideCreate with params');
    const url = `/lessons/${lessonId}/slides/create?from=course-edit&courseId=${courseId}`;
    console.log('Navigating to:', url);
    navigate(url);
  };

  const testCourseEdit = () => {
    const courseId = '1';
    console.log('Testing navigation to CourseEdit');
    const url = `/courses/${courseId}/edit?tab=content`;
    console.log('Navigating to:', url);
    navigate(url);
  };

  const testDirectNavigation = () => {
    const courseId = '1';
    console.log('Testing direct navigation to CourseEdit');
    navigate(`/courses/${courseId}/edit`);
  };

  return (
    <Container className="mt-4">
      <Card>
        <Card.Header>
          <h4>Navigation Debug Test</h4>
        </Card.Header>
        <Card.Body>
          <Alert variant="info">
            <strong>Current URL:</strong> {window.location.href}
          </Alert>

          <Alert variant="secondary">
            <strong>URL Params:</strong>
            <pre>{JSON.stringify(Object.fromEntries(searchParams.entries()), null, 2)}</pre>
          </Alert>

          <Alert variant="secondary">
            <strong>Route Params:</strong>
            <pre>{JSON.stringify(params, null, 2)}</pre>
          </Alert>

          <div className="d-grid gap-2">
            <Button variant="primary" onClick={testNavigation}>
              Test Navigation to SlideCreate with params
            </Button>

            <Button variant="success" onClick={testCourseEdit}>
              Test Navigation to CourseEdit with tab
            </Button>

            <Button variant="secondary" onClick={testDirectNavigation}>
              Test Direct Navigation to CourseEdit
            </Button>

            <Button variant="outline-secondary" onClick={() => navigate('/dashboard')}>
              Back to Dashboard
            </Button>
          </div>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default NavigationTest;
