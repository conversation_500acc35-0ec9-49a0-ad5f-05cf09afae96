import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, Alert, Form } from 'react-bootstrap';
import { slideService } from '../services/slideService';

const OrderIndexTest: React.FC = () => {
  const [lessonId, setLessonId] = useState('14');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testOrderIndexCalculation = async () => {
    setLoading(true);
    setResult(null);

    try {
      console.log('=== ORDER INDEX TEST START ===');
      
      // Test fetching slides
      const existingSlides = await slideService.getSlidesByLessonId(parseInt(lessonId));
      console.log('Existing slides:', existingSlides);
      console.log('Existing slides type:', typeof existingSlides);
      console.log('Existing slides is array:', Array.isArray(existingSlides));

      // Test order index calculation
      let nextOrderIndex = 1; // Default to 1

      // Ensure existingSlides is a valid array
      const validSlides = Array.isArray(existingSlides) ? existingSlides : [];

      if (validSlides.length > 0) {
        // Filter out slides with invalid order_index and ensure we have valid numbers
        const validOrderIndexes = validSlides
          .map(slide => slide.order_index)
          .filter(index => typeof index === 'number' && !isNaN(index) && index > 0);

        if (validOrderIndexes.length > 0) {
          const maxOrder = Math.max(...validOrderIndexes);
          nextOrderIndex = maxOrder + 1;
        }
      }

      // Ensure nextOrderIndex is always >= 1
      if (nextOrderIndex < 1) {
        nextOrderIndex = 1;
      }

      const calculationResult = {
        existingSlidesLength: validSlides.length,
        existingOrderIndexes: validSlides.map(s => s?.order_index),
        calculatedNextOrderIndex: nextOrderIndex,
        validSlides: validSlides
      };

      console.log('Order index calculation:', calculationResult);

      // Test slide data creation
      const slideData = {
        lesson_id: parseInt(lessonId),
        title: 'Test Slide',
        content: 'Test Content',
        image_url: undefined,
        order_index: nextOrderIndex,
      };

      console.log('Test slide data:', slideData);

      setResult({
        success: true,
        existingSlides,
        calculation: calculationResult,
        slideData,
        message: `Order index calculated: ${nextOrderIndex}`
      });

      console.log('=== ORDER INDEX TEST END ===');

    } catch (error) {
      console.error('Test error:', error);
      setResult({
        success: false,
        error: error.message,
        message: 'Test failed'
      });
    } finally {
      setLoading(false);
    }
  };

  const testCreateSlide = async () => {
    setLoading(true);
    
    try {
      console.log('=== CREATE SLIDE TEST START ===');
      
      // Get existing slides first
      const existingSlides = await slideService.getSlidesByLessonId(parseInt(lessonId));
      
      // Calculate order index
      let nextOrderIndex = 1;
      const validSlides = Array.isArray(existingSlides) ? existingSlides : [];
      
      if (validSlides.length > 0) {
        const validOrderIndexes = validSlides
          .map(slide => slide.order_index)
          .filter(index => typeof index === 'number' && !isNaN(index) && index > 0);
        
        if (validOrderIndexes.length > 0) {
          const maxOrder = Math.max(...validOrderIndexes);
          nextOrderIndex = maxOrder + 1;
        }
      }
      
      if (nextOrderIndex < 1) {
        nextOrderIndex = 1;
      }

      // Create test slide
      const slideData = {
        lesson_id: parseInt(lessonId),
        title: `Test Slide ${Date.now()}`,
        content: 'This is a test slide created for debugging order_index',
        order_index: nextOrderIndex,
      };

      console.log('Creating slide with data:', slideData);
      
      const createdSlide = await slideService.createSlide(slideData);
      
      console.log('Created slide:', createdSlide);
      
      setResult({
        success: true,
        createdSlide,
        message: `Slide created successfully with order_index: ${createdSlide.order_index}`
      });

      console.log('=== CREATE SLIDE TEST END ===');

    } catch (error) {
      console.error('Create slide test error:', error);
      setResult({
        success: false,
        error: error.message,
        message: 'Create slide test failed'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="mt-4">
      <Card>
        <Card.Header>
          <h4>Order Index Debug Test</h4>
        </Card.Header>
        <Card.Body>
          <Form.Group className="mb-3">
            <Form.Label>Lesson ID</Form.Label>
            <Form.Control
              type="number"
              value={lessonId}
              onChange={(e) => setLessonId(e.target.value)}
              placeholder="Enter lesson ID"
            />
          </Form.Group>

          <div className="d-grid gap-2 mb-3">
            <Button 
              variant="primary" 
              onClick={testOrderIndexCalculation}
              disabled={loading}
            >
              {loading ? 'Testing...' : 'Test Order Index Calculation'}
            </Button>
            
            <Button 
              variant="success" 
              onClick={testCreateSlide}
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Test Create Slide'}
            </Button>
          </div>

          {result && (
            <Alert variant={result.success ? 'success' : 'danger'}>
              <strong>{result.message}</strong>
              <pre className="mt-2 small">
                {JSON.stringify(result, null, 2)}
              </pre>
            </Alert>
          )}

          <Alert variant="info">
            <strong>Instructions:</strong>
            <ol className="mb-0 mt-2">
              <li>Open browser console (F12)</li>
              <li>Enter lesson ID (default: 14)</li>
              <li>Click "Test Order Index Calculation" to see calculation logic</li>
              <li>Click "Test Create Slide" to actually create a test slide</li>
              <li>Check console logs for detailed debugging info</li>
            </ol>
          </Alert>
        </Card.Body>
      </Card>
    </Container>
  );
};

export default OrderIndexTest;
