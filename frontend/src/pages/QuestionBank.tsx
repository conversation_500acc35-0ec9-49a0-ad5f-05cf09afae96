import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Modal, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from 'react-bootstrap';
import { Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';
import {
  getQuestionBankCategories,
  getBankQuestions,
  deleteBankQuestion,
  type GetBankQuestionsParams
} from '../services/questionBankService';
import { type QuestionBankCategory, type BankQuestion, type DifficultyLevel } from '../types';
import { PagingComponent, PaginationInfo, QuestionBankForm } from '../components';

const QuestionBank: React.FC = () => {
  const [categories, setCategories] = useState<QuestionBankCategory[]>([]);
  const [questions, setQuestions] = useState<BankQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<BankQuestion | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingQuestion, setDeletingQuestion] = useState<BankQuestion | null>(null);

  // Filters
  const [selectedCategory, setSelectedCategory] = useState<number | ''>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<DifficultyLevel | ''>('');
  const [selectedType, setSelectedType] = useState<'single_choice' | 'multiple_choice' | ''>('');
  const [searchTerm, setSearchTerm] = useState('');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalQuestions, setTotalQuestions] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const limit = 10;

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    loadQuestions();
  }, [selectedCategory, selectedDifficulty, selectedType, searchTerm, currentPage]);

  const loadCategories = async () => {
    try {
      const response = await getQuestionBankCategories();
      setCategories(response.categories);
    } catch (err) {
      setError('Failed to load categories');
      console.error('Error loading categories:', err);
    }
  };

  const loadQuestions = async () => {
    try {
      setLoading(true);
      const params: GetBankQuestionsParams = {
        page: currentPage,
        limit,
      };

      if (selectedCategory) params.category_id = Number(selectedCategory);
      if (selectedDifficulty) params.difficulty = selectedDifficulty;
      if (selectedType) params.question_type = selectedType;
      if (searchTerm.trim()) params.search = searchTerm.trim();

      const response = await getBankQuestions(params);
      setQuestions(response.questions);
      setTotalQuestions(response.total);
      setTotalPages(Math.ceil(response.total / limit));
    } catch (err) {
      setError('Failed to load questions');
      console.error('Error loading questions:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateQuestion = () => {
    setEditingQuestion(null);
    setShowForm(true);
  };

  const handleEditQuestion = (question: BankQuestion) => {
    setEditingQuestion(question);
    setShowForm(true);
  };

  const handleDeleteQuestion = (question: BankQuestion) => {
    setDeletingQuestion(question);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!deletingQuestion) return;

    try {
      await deleteBankQuestion(deletingQuestion.id);
      setShowDeleteModal(false);
      setDeletingQuestion(null);
      loadQuestions();
    } catch (err) {
      setError('Failed to delete question');
      console.error('Error deleting question:', err);
    }
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingQuestion(null);
    loadQuestions();
    loadCategories(); // Reload categories in case a new one was created
  };

  const handleCategoriesReload = () => {
    loadCategories(); // Only reload categories, don't close form
  };

  const resetFilters = () => {
    setSelectedCategory('');
    setSelectedDifficulty('');
    setSelectedType('');
    setSearchTerm('');
    setCurrentPage(1);
  };

  const getDifficultyBadgeVariant = (difficulty: DifficultyLevel) => {
    switch (difficulty) {
      case 'easy': return 'success';
      case 'normal': return 'warning';
      case 'hard': return 'danger';
      default: return 'secondary';
    }
  };

  const getTypeBadgeVariant = (type: string) => {
    return type === 'single_choice' ? 'primary' : 'info';
  };

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h2>Ngân hàng câu hỏi</h2>
            <Button variant="primary" onClick={handleCreateQuestion}>
              <Plus size={20} className="me-2" />
              Thêm câu hỏi
            </Button>
          </div>
        </Col>
      </Row>

      {error && (
        <Row className="mb-3">
          <Col>
            <Alert variant="danger" dismissible onClose={() => setError(null)}>
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      {/* Filters */}
      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Body>
              <Row className="g-3">
                <Col md={3}>
                  <Form.Group>
                    <Form.Label>Tìm kiếm</Form.Label>
                    <div className="position-relative">
                      <Search size={16} className="position-absolute top-50 start-0 translate-middle-y ms-2 text-muted" />
                      <Form.Control
                        type="text"
                        placeholder="Tìm theo nội dung hoặc tag..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="ps-5"
                      />
                    </div>
                  </Form.Group>
                </Col>
                <Col md={2}>
                  <Form.Group>
                    <Form.Label>Danh mục</Form.Label>
                    <Form.Select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value as number | '')}
                    >
                      <option value="">Tất cả</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={2}>
                  <Form.Group>
                    <Form.Label>Độ khó</Form.Label>
                    <Form.Select
                      value={selectedDifficulty}
                      onChange={(e) => setSelectedDifficulty(e.target.value as DifficultyLevel | '')}
                    >
                      <option value="">Tất cả</option>
                      <option value="easy">Dễ</option>
                      <option value="normal">Trung bình</option>
                      <option value="hard">Khó</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={2}>
                  <Form.Group>
                    <Form.Label>Loại câu hỏi</Form.Label>
                    <Form.Select
                      value={selectedType}
                      onChange={(e) => setSelectedType(e.target.value as 'single_choice' | 'multiple_choice' | '')}
                    >
                      <option value="">Tất cả</option>
                      <option value="single_choice">Một đáp án</option>
                      <option value="multiple_choice">Nhiều đáp án</option>
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={3} className="d-flex align-items-end">
                  <Button variant="outline-secondary" onClick={resetFilters}>
                    <Filter size={16} className="me-2" />
                    Xóa bộ lọc
                  </Button>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Questions List */}
      <Row>
        <Col>
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" />
              <div className="mt-2">Đang tải...</div>
            </div>
          ) : questions.length === 0 ? (
            <Card>
              <Card.Body className="text-center py-5">
                <h5>Không có câu hỏi nào</h5>
                <p className="text-muted">Hãy thêm câu hỏi đầu tiên vào ngân hàng</p>
                <Button variant="primary" onClick={handleCreateQuestion}>
                  <Plus size={20} className="me-2" />
                  Thêm câu hỏi
                </Button>
              </Card.Body>
            </Card>
          ) : (
            <>
              {questions.map((question) => (
                <Card key={question.id} className="mb-3">
                  <Card.Body>
                    <Row>
                      <Col md={8}>
                        <div className="d-flex align-items-start mb-2">
                          <Badge
                            bg={getDifficultyBadgeVariant(question.difficulty)}
                            className="me-2"
                          >
                            {question.difficulty === 'easy' ? 'Dễ' :
                              question.difficulty === 'normal' ? 'TB' : 'Khó'}
                          </Badge>
                          <Badge
                            bg={getTypeBadgeVariant(question.question_type)}
                            className="me-2"
                          >
                            {question.question_type === 'single_choice' ? 'Một đáp án' : 'Nhiều đáp án'}
                          </Badge>
                          <Badge bg="secondary">
                            {question.points} điểm
                          </Badge>
                        </div>
                        <h6 className="mb-2">{question.question_text}</h6>
                        <div className="text-muted small">
                          <div>Danh mục: {question.category?.name}</div>
                          <div>Tạo bởi: {question.creator?.first_name} {question.creator?.last_name}</div>
                          {question.tags && <div>Tags: {question.tags}</div>}
                        </div>
                      </Col>
                      <Col md={4} className="text-end">
                        <div className="btn-group">
                          <Button
                            variant="outline-info"
                            size="sm"
                            onClick={() => handleEditQuestion(question)}
                          >
                            <Eye size={16} />
                          </Button>
                          <Button
                            variant="outline-warning"
                            size="sm"
                            onClick={() => handleEditQuestion(question)}
                          >
                            <Edit size={16} />
                          </Button>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => handleDeleteQuestion(question)}
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>
              ))}

              {/* Pagination Info and Controls */}
              <div className="d-flex justify-content-between align-items-center mt-4">
                <PaginationInfo
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalQuestions}
                  itemsPerPage={limit}
                />
                <PagingComponent
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={setCurrentPage}
                  maxVisiblePages={10}
                />
              </div>
            </>
          )}
        </Col>
      </Row>

      {/* Question Form Modal */}
      <QuestionBankForm
        show={showForm}
        onHide={() => setShowForm(false)}
        question={editingQuestion}
        categories={categories}
        onSuccess={handleFormSuccess}
        onCategoriesReload={handleCategoriesReload}
      />

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Xác nhận xóa</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p>Bạn có chắc chắn muốn xóa câu hỏi này?</p>
          {deletingQuestion && (
            <div className="bg-light p-3 rounded">
              <strong>{deletingQuestion.question_text}</strong>
            </div>
          )}
          <p className="text-danger mt-2 mb-0">
            <small>Lưu ý: Không thể xóa câu hỏi đang được sử dụng trong quiz.</small>
          </p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Hủy
          </Button>
          <Button variant="danger" onClick={confirmDelete}>
            Xóa
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default QuestionBank;
