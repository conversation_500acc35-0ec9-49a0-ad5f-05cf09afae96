import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, Card, Form, ProgressBar, Alert, Badge } from 'react-bootstrap';
import { type Quiz } from '../types';
import { quizService } from '../services/quizService';

const QuizAttempt: React.FC = () => {
  const { quizId } = useParams<{ quizId: string }>();
  const navigate = useNavigate();

  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [questions, setQuestions] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Map<number, number[]>>(new Map());
  const [attemptId, setAttemptId] = useState<number | null>(null);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [submitted, setSubmitted] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [selectedOptionIndex, setSelectedOptionIndex] = useState(0);

  // Fetch quiz data and generate questions
  useEffect(() => {
    const fetchQuiz = async () => {
      try {
        setLoading(true);
        const quizData = await quizService.getQuiz(parseInt(quizId!));
        setQuiz(quizData);

        // Start quiz attempt
        const attempt = await quizService.startQuizAttempt(parseInt(quizId!));
        setAttemptId(attempt.id);

        // Generate dynamic questions for this attempt
        await quizService.generateQuizQuestions(attempt.id);

        // Get the generated questions
        const questionsData = await quizService.getQuizAttemptQuestions(attempt.id);
        setQuestions(questionsData.questions || []);

        // Set timer if quiz has time limit
        if (quizData.time_limit > 0) {
          setTimeLeft(quizData.time_limit * 60);
        }
      } catch (err: any) {
        setError(err.response?.data?.error || 'Failed to load quiz');
      } finally {
        setLoading(false);
      }
    };

    fetchQuiz();
  }, [quizId]);

  // Timer countdown
  useEffect(() => {
    if (!timeLeft) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev && prev > 0) {
          return prev - 1;
        } else {
          clearInterval(timer);
          handleSubmit();
          return 0;
        }
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (submitted || !questions.length) return;

      const questionData = questions[currentQuestion];
      const question = questionData?.bank_question || questionData;
      const options = question?.options || [];
      const bankQuestionId = questionData?.bank_question_id || questionData?.bank_question?.id || questionData?.id;
      const isMultipleChoice = question?.question_type === 'multiple_choice';

      switch (e.key) {
        case 'ArrowDown':
        case 'j': // Vim-style navigation
          e.preventDefault();
          setSelectedOptionIndex(prev => Math.min(prev + 1, options.length - 1));
          break;
        case 'ArrowUp':
        case 'k': // Vim-style navigation
          e.preventDefault();
          setSelectedOptionIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'ArrowLeft':
        case 'h': // Vim-style navigation
          e.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
        case 'l': // Vim-style navigation
          e.preventDefault();
          handleNext();
          break;
        case ' ': // Spacebar to select option
        case 'Enter':
          e.preventDefault();
          if (options[selectedOptionIndex]) {
            handleOptionSelect(bankQuestionId, options[selectedOptionIndex].id, isMultipleChoice);
          }
          break;
        case 's': // Submit quiz
          if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            if (currentQuestion === questions.length - 1) {
              handleSubmit();
            }
          }
          break;
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
          e.preventDefault();
          const optionIndex = parseInt(e.key) - 1;
          if (options[optionIndex]) {
            setSelectedOptionIndex(optionIndex);
            handleOptionSelect(bankQuestionId, options[optionIndex].id, isMultipleChoice);
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentQuestion, questions, submitted, selectedOptionIndex]);

  // Reset selected option index when question changes
  useEffect(() => {
    setSelectedOptionIndex(0);
  }, [currentQuestion]);

  const handleOptionSelect = (questionId: number, optionId: number, isMultiple: boolean) => {
    setAnswers(prev => {
      const newAnswers = new Map(prev);

      if (isMultiple) {
        // For multiple choice questions
        const currentSelections = prev.get(questionId) || [];
        if (currentSelections.includes(optionId)) {
          // Remove if already selected
          newAnswers.set(questionId, currentSelections.filter(id => id !== optionId));
        } else {
          // Add to selections
          newAnswers.set(questionId, [...currentSelections, optionId]);
        }
      } else {
        // For single choice questions
        newAnswers.set(questionId, [optionId]);
      }

      return newAnswers;
    });
  };

  const handleNext = () => {
    if (questions && currentQuestion < questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    if (!attemptId || !quiz || !questions.length) return;

    try {
      setLoading(true);

      // Format answers for API - answers Map already uses bank_question_id as key
      const formattedAnswers = Array.from(answers.entries()).map(([bankQuestionId, optionIds]) => {
        console.log('Bank question ID:', bankQuestionId);
        console.log('Selected option IDs:', optionIds);

        return {
          question_id: bankQuestionId,
          option_ids: optionIds
        };
      });

      console.log('Formatted answers:', formattedAnswers);

      const result = await quizService.submitQuizAttempt(attemptId, {
        answers: formattedAnswers
      });

      setResult(result);
      setSubmitted(true);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to submit quiz');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !quiz) {
    return <div className="text-center p-5">Loading quiz...</div>;
  }

  if (error) {
    return <Alert variant="danger">{error}</Alert>;
  }

  if (!quiz) {
    return <Alert variant="warning">Quiz not found</Alert>;
  }

  if (!questions.length && !loading) {
    return <Alert variant="warning">No questions available for this quiz</Alert>;
  }

  if (submitted && result) {
    return (
      <Card className="quiz-result">
        <Card.Header>
          <h4>Quiz Results: {quiz.title}</h4>
        </Card.Header>
        <Card.Body>
          <div className="text-center mb-4">
            <h5>Your Score: {result.percentage.toFixed(1)}%</h5>
            <ProgressBar
              now={result.percentage}
              variant={result.is_passed ? "success" : "danger"}
              className="my-3"
            />
            <div className="mt-2">
              {result.is_passed ?
                <Alert variant="success">Congratulations! You passed the quiz.</Alert> :
                <Alert variant="warning">You did not pass the quiz. Required score: {quiz.pass_score}%</Alert>
              }
            </div>
          </div>
          <div className="d-flex justify-content-between">
            <Button variant="outline-primary" onClick={() => navigate(`/courses/${quiz.course_id}`)}>
              Back to Course
            </Button>

            <Button variant="outline-secondary" onClick={() => navigate(`/quizzes/${quiz.id}/history`)}>
              View History
            </Button>

            <Button variant="primary" onClick={() => navigate('/dashboard')}>
              Go to Dashboard
            </Button>
          </div>
        </Card.Body>
      </Card>
    );
  }

  const questionData = questions[currentQuestion];
  const question = questionData?.bank_question || questionData;
  const isMultipleChoice = question?.question_type === 'multiple_choice';
  const bankQuestionId = questionData?.bank_question_id || questionData?.bank_question?.id || questionData?.id;
  const selectedOptions = answers.get(bankQuestionId) || [];
  const formattedTimeLeft = timeLeft ?
    `${Math.floor(timeLeft / 60)}:${(timeLeft % 60).toString().padStart(2, '0')}` :
    null;

  return (
    <div className="quiz-attempt-container">
      <Card>
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <h4>{quiz.title}</h4>
            {formattedTimeLeft && (
              <div className="quiz-timer">
                Time Left: <span className={timeLeft && timeLeft < 60 ? "text-danger" : ""}>
                  {formattedTimeLeft}
                </span>
              </div>
            )}
          </div>
          <div className="mt-2">
            <small className="text-muted">
              Use ↑↓/j/k to navigate options, ←→/h/l for questions, Space/Enter to select, 1-5 for quick select
            </small>
          </div>
        </Card.Header>
        <Card.Body>
          <div className="mb-3">
            <ProgressBar
              now={(currentQuestion + 1) / questions.length * 100}
              label={`${currentQuestion + 1}/${questions.length}`}
            />
          </div>

          <div className="question-container mb-4">
            <h5>{question?.text || question?.question_text}</h5>
            {question?.image_url && (
              <div className="text-center my-3">
                <img
                  src={question.image_url}
                  alt="Question"
                  className="img-fluid question-image"
                  style={{ maxHeight: '300px' }}
                />
              </div>
            )}

            <Form className="mt-3">
              {question?.options?.map((option, index) => (
                <div
                  key={option.id}
                  className={`p-2 mb-2 rounded border ${index === selectedOptionIndex
                    ? 'border-primary bg-primary bg-opacity-10'
                    : 'border-light'
                    }`}
                  style={{
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={() => {
                    setSelectedOptionIndex(index);
                    handleOptionSelect(bankQuestionId, option.id, isMultipleChoice);
                  }}
                  onMouseEnter={() => setSelectedOptionIndex(index)}
                >
                  <Form.Check
                    type={isMultipleChoice ? "checkbox" : "radio"}
                    id={`option-${option.id}`}
                    name={`question-${currentQuestion}`}
                    label={
                      <span>
                        <Badge bg="secondary" className="me-2">
                          {index + 1}
                        </Badge>
                        {option.text || option.option_text}
                        {index === selectedOptionIndex && (
                          <span className="ms-2 text-primary">
                            <small>← Selected</small>
                          </span>
                        )}
                      </span>
                    }
                    checked={selectedOptions.includes(option.id)}
                    onChange={() => handleOptionSelect(bankQuestionId, option.id, isMultipleChoice)}
                    className="mb-0"
                  />
                </div>
              ))}
            </Form>
          </div>

          <div className="d-flex justify-content-between">
            <Button
              variant="outline-secondary"
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
            >
              Previous
            </Button>

            {currentQuestion < questions.length - 1 ? (
              <Button variant="primary" onClick={handleNext}>
                Next
              </Button>
            ) : (
              <Button variant="success" onClick={handleSubmit}>
                Submit Quiz
              </Button>
            )}
          </div>
        </Card.Body>
      </Card>
    </div>
  );
};

export default QuizAttempt;