import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ge, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col } from 'react-bootstrap';
import { ArrowLeft, Clock, CheckCircle, XCircle, Award, Target } from 'lucide-react';
import { getQuizAttemptDetail } from '../services/quizHistoryService';
import type { QuizAttemptDetail } from '../types';

const QuizAttemptDetailPage: React.FC = () => {
  const { attemptId } = useParams<{ attemptId: string }>();
  const navigate = useNavigate();
  const [detail, setDetail] = useState<QuizAttemptDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (attemptId) {
      loadDetail();
    }
  }, [attemptId]);

  const loadDetail = async () => {
    try {
      setLoading(true);
      const data = await getQuizAttemptDetail(parseInt(attemptId!));
      setDetail(data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load attempt detail');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const calculateDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);
    return `${diffMins}:${diffSecs.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <Spinner animation="border" />
          <p className="mt-2">Đang tải chi tiết bài thi...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );
  }

  if (!detail) {
    return (
      <Container className="mt-4">
        <Alert variant="info">Không tìm thấy chi tiết bài thi</Alert>
      </Container>
    );
  }

  const { attempt, questions } = detail;

  // Helper function to get user's answers for a question (now from question.user_answers)
  const getUserAnswersForQuestion = (question: any) => {
    return question.user_answers || [];
  };

  // Helper function to check if user got the question correct
  const isQuestionCorrect = (question: any) => {
    const userAnswers = getUserAnswersForQuestion(question);
    const correctOptions = question.bank_question?.options?.filter((opt: any) => opt.is_correct) || [];
    const correctOptionIds = correctOptions.map((opt: any) => opt.id);

    // For single choice: user must select exactly the correct option
    if (question.bank_question?.question_type === 'single_choice') {
      return userAnswers.length === 1 && correctOptionIds.includes(userAnswers[0]);
    }

    // For multiple choice: user must select all correct options and no incorrect ones
    if (question.bank_question?.question_type === 'multiple_choice') {
      return userAnswers.length === correctOptionIds.length &&
        userAnswers.every((id: number) => correctOptionIds.includes(id)) &&
        correctOptionIds.every((id: number) => userAnswers.includes(id));
    }

    return false;
  };

  return (
    <Container className="mt-4">
      {/* Header */}
      <Card className="mb-4">
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h4 className="mb-1">
                <Award className="me-2" />
                Chi tiết bài thi: {attempt.quiz?.title}
              </h4>
              <div className="text-muted small">
                Thời gian: {formatDate(attempt.started_at)} - {formatDate(attempt.completed_at)}
              </div>
            </div>
            <Button variant="outline-secondary" onClick={() => navigate(-1)}>
              <ArrowLeft size={16} className="me-1" />
              Quay lại
            </Button>
          </div>
        </Card.Header>

        <Card.Body>
          <Row>
            <Col md={3}>
              <div className="text-center">
                <div className="mb-2">
                  {attempt.is_passed ? (
                    <CheckCircle size={48} className="text-success" />
                  ) : (
                    <XCircle size={48} className="text-danger" />
                  )}
                </div>
                <Badge
                  bg={attempt.is_passed ? 'success' : 'danger'}
                  className="fs-5 px-3 py-2"
                >
                  {attempt.percentage.toFixed(1)}%
                </Badge>
                <div className="mt-2 text-muted">
                  {attempt.is_passed ? 'ĐẠT' : 'KHÔNG ĐẠT'}
                </div>
              </div>
            </Col>

            <Col md={9}>
              <Row>
                <Col sm={6}>
                  <div className="mb-3">
                    <div className="text-muted small">Điểm số</div>
                    <div className="h5">
                      <Target className="me-1" />
                      {attempt.score.toFixed(1)} / {attempt.max_score.toFixed(1)}
                    </div>
                  </div>
                </Col>

                <Col sm={6}>
                  <div className="mb-3">
                    <div className="text-muted small">Thời gian làm bài</div>
                    <div className="h5">
                      <Clock className="me-1" />
                      {calculateDuration(attempt.started_at, attempt.completed_at)}
                    </div>
                  </div>
                </Col>

                <Col sm={6}>
                  <div className="mb-3">
                    <div className="text-muted small">Điểm cần đạt</div>
                    <div className="h5">
                      {attempt.quiz?.pass_score}%
                    </div>
                  </div>
                </Col>

                <Col sm={6}>
                  <div className="mb-3">
                    <div className="text-muted small">Trạng thái</div>
                    <div className="h5">
                      {attempt.is_passed ? (
                        <Badge bg="success">Đạt yêu cầu</Badge>
                      ) : (
                        <Badge bg="danger">Chưa đạt yêu cầu</Badge>
                      )}
                    </div>
                  </div>
                </Col>
              </Row>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Questions Detail */}
      {questions && questions.length > 0 && (
        <Card>
          <Card.Header>
            <h5 className="mb-0">Chi tiết câu hỏi và đáp án</h5>
          </Card.Header>
          <Card.Body>
            {questions.map((question, index) => {
              const userAnswers = getUserAnswersForQuestion(question);
              const questionCorrect = isQuestionCorrect(question);

              return (
                <Card key={question.id} className="mb-3">
                  <Card.Body>
                    <div className="d-flex justify-content-between align-items-start mb-3">
                      <h6>
                        <Badge bg="secondary" className="me-2">
                          Câu {index + 1}
                        </Badge>
                        {question.bank_question?.question_text}
                        {questionCorrect ? (
                          <CheckCircle size={20} className="text-success ms-2" />
                        ) : (
                          <XCircle size={20} className="text-danger ms-2" />
                        )}
                      </h6>
                      <div className="text-end">
                        <Badge bg="info" className="me-2">
                          {question.points} điểm
                        </Badge>
                        <Badge bg={questionCorrect ? 'success' : 'danger'}>
                          {questionCorrect ? `+${question.points}` : '0'} điểm
                        </Badge>
                      </div>
                    </div>

                    <div className="ms-4">
                      {question.bank_question?.options?.map((option) => {
                        const isUserSelected = userAnswers.includes(option.id);
                        const isCorrect = option.is_correct;
                        const showCorrectAnswers = question.show_correct_answers;

                        let bgClass = 'bg-light';
                        let borderClass = '';

                        if (showCorrectAnswers) {
                          if (isCorrect && isUserSelected) {
                            // User selected correct answer
                            bgClass = 'bg-success bg-opacity-10';
                            borderClass = 'border border-success';
                          } else if (isCorrect && !isUserSelected) {
                            // Correct answer not selected by user
                            bgClass = 'bg-success bg-opacity-10';
                            borderClass = 'border border-success border-opacity-50';
                          } else if (!isCorrect && isUserSelected) {
                            // User selected wrong answer
                            bgClass = 'bg-danger bg-opacity-10';
                            borderClass = 'border border-danger';
                          }
                        } else {
                          // Only highlight user selections when correct answers are hidden
                          if (isUserSelected) {
                            bgClass = 'bg-primary bg-opacity-10';
                            borderClass = 'border border-primary';
                          }
                        }

                        return (
                          <div
                            key={option.id}
                            className={`p-2 mb-2 rounded ${bgClass} ${borderClass}`}
                          >
                            <div className="d-flex align-items-center">
                              {showCorrectAnswers && isCorrect && (
                                <CheckCircle size={16} className="text-success me-2" />
                              )}
                              {showCorrectAnswers && !isCorrect && isUserSelected && (
                                <XCircle size={16} className="text-danger me-2" />
                              )}
                              <span>{option.option_text}</span>

                              <div className="ms-auto d-flex gap-2">
                                {isUserSelected && (
                                  <Badge bg={showCorrectAnswers ? (isCorrect ? 'success' : 'danger') : 'primary'}>
                                    {showCorrectAnswers
                                      ? (isCorrect ? 'Bạn chọn ✓' : 'Bạn chọn ✗')
                                      : 'Bạn đã chọn'
                                    }
                                  </Badge>
                                )}
                                {showCorrectAnswers && isCorrect && (
                                  <Badge bg="success" className={isUserSelected ? '' : 'opacity-75'}>
                                    Đáp án đúng
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </Card.Body>
                </Card>
              );
            })}
          </Card.Body>
        </Card>
      )}
    </Container>
  );
};

export default QuizAttemptDetailPage;
