import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, <PERSON><PERSON>, Form, Alert, Spinner, Badge } from 'react-bootstrap';
import { ArrowLeft, Save, Settings, Database } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { quizService, type UpdateQuizRequest } from '../services/quizService';
import { validateQuizRules } from '../services/quizRuleService';
import { type Quiz } from '../types';
import { QuizRulesList } from '../components';

const QuizEdit: React.FC = () => {
  const { quizId } = useParams<{ quizId: string }>();
  const navigate = useNavigate();
  const { state } = useAuth();

  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'settings' | 'rules'>('settings');
  const [rulesValidation, setRulesValidation] = useState<{ valid: boolean; total_questions: number } | null>(null);

  const isTeacherOrAdmin = state.user?.role === 'teacher' || state.user?.role === 'admin';

  const [quizForm, setQuizForm] = useState<UpdateQuizRequest>({
    title: '',
    description: '',
    time_limit: 0,
    pass_score: 70,
    is_published: false,
    total_questions: 10,

    // Quiz attempt settings
    max_attempts: 0,
    show_results: true,
    show_correct_answer: false,
    allow_review: true,
  });

  useEffect(() => {
    if (!quizId || !isTeacherOrAdmin) {
      navigate('/quizzes');
      return;
    }
    fetchQuizData();
  }, [quizId, isTeacherOrAdmin, navigate]);

  const fetchQuizData = async () => {
    try {
      setLoading(true);
      const quizData = await quizService.getQuiz(parseInt(quizId!));
      setQuiz(quizData);

      setQuizForm({
        title: quizData.title,
        description: quizData.description,
        time_limit: quizData.time_limit,
        pass_score: quizData.pass_score,
        is_published: quizData.is_published,
        total_questions: quizData.total_questions,

        // Quiz attempt settings
        max_attempts: quizData.max_attempts || 0,
        show_results: quizData.show_results ?? true,
        show_correct_answer: quizData.show_correct_answer ?? false,
        allow_review: quizData.allow_review ?? true,
      });

      // Load rules validation
      await loadRulesValidation();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load quiz');
    } finally {
      setLoading(false);
    }
  };

  const loadRulesValidation = async () => {
    try {
      const validation = await validateQuizRules(parseInt(quizId!));
      setRulesValidation({
        valid: validation.valid,
        total_questions: validation.total_questions
      });
    } catch (err) {
      // Validation might fail if no rules exist yet, which is fine
      setRulesValidation(null);
    }
  };

  const handleSaveQuiz = async () => {
    try {
      setSaving(true);
      await quizService.updateQuiz(parseInt(quizId!), quizForm);
      await fetchQuizData();
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to save quiz');
    } finally {
      setSaving(false);
    }
  };

  const handleRulesUpdate = () => {
    // Reload validation when rules are updated
    loadRulesValidation();
  };

  if (!isTeacherOrAdmin) {
    return (
      <Container>
        <Alert variant="danger">
          <strong>Access Denied:</strong> You don't have permission to edit quizzes.
        </Alert>
      </Container>
    );
  }

  if (loading) {
    return (
      <Container>
        <div className="text-center py-5">
          <Spinner animation="border" role="status" className="mb-3">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="text-muted">Loading quiz...</p>
        </div>
      </Container>
    );
  }

  if (error && !quiz) {
    return (
      <Container>
        <Alert variant="danger">
          <strong>Error:</strong> {error}
          <div className="mt-2">
            <Button variant="outline-secondary" onClick={() => navigate('/quizzes')}>
              <ArrowLeft size={16} className="me-1" />
              Back to Quizzes
            </Button>
          </div>
        </Alert>
      </Container>
    );
  }

  return (
    <Container>
      <div className="mb-4">
        <Button variant="outline-secondary" onClick={() => navigate('/quizzes')} className="mb-3">
          <ArrowLeft size={16} className="me-1" />
          Back to Quizzes
        </Button>

        <div className="d-flex justify-content-between align-items-center">
          <h1>Edit Quiz: {quiz?.title}</h1>
          <Badge bg={quiz?.is_published ? 'success' : 'warning'}>
            {quiz?.is_published ? 'Published' : 'Draft'}
          </Badge>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <strong>Error:</strong> {error}
          <Button
            variant="link"
            className="p-0 ms-2"
            onClick={() => setError(null)}
          >
            Dismiss
          </Button>
        </Alert>
      )}

      {/* Tab Navigation */}
      <Card className="mb-4">
        <Card.Header>
          <div className="d-flex gap-3">
            <Button
              variant={activeTab === 'settings' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('settings')}
              className="d-flex align-items-center"
            >
              <Settings size={16} className="me-2" />
              Cài đặt Quiz
            </Button>
            <Button
              variant={activeTab === 'rules' ? 'primary' : 'outline-primary'}
              onClick={() => setActiveTab('rules')}
              className="d-flex align-items-center"
            >
              <Database size={16} className="me-2" />
              Quy tắc câu hỏi
              {rulesValidation && (
                <Badge
                  bg={rulesValidation.valid ? 'success' : 'warning'}
                  className="ms-2"
                >
                  {rulesValidation.total_questions}
                </Badge>
              )}
            </Button>
          </div>
        </Card.Header>
      </Card>

      <Row>
        <Col lg={8}>
          {activeTab === 'settings' && (
            /* Quiz Settings */
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">Cài đặt Quiz</h5>
              </Card.Header>
              <Card.Body>
                <Form>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Quiz Title</Form.Label>
                        <Form.Control
                          type="text"
                          value={quizForm.title}
                          onChange={(e) => setQuizForm({ ...quizForm, title: e.target.value })}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Pass Score (%)</Form.Label>
                        <Form.Control
                          type="number"
                          min="0"
                          max="100"
                          value={quizForm.pass_score}
                          onChange={(e) => setQuizForm({ ...quizForm, pass_score: parseInt(e.target.value) || 70 })}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      value={quizForm.description}
                      onChange={(e) => setQuizForm({ ...quizForm, description: e.target.value })}
                    />
                  </Form.Group>

                  <Row>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>Total Questions</Form.Label>
                        <Form.Control
                          type="number"
                          min="1"
                          max="100"
                          value={quizForm.total_questions}
                          onChange={(e) => setQuizForm({ ...quizForm, total_questions: parseInt(e.target.value) || 10 })}
                        />
                        <Form.Text className="text-muted">
                          Number of questions to generate
                        </Form.Text>
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Label>Time Limit (minutes)</Form.Label>
                        <Form.Control
                          type="number"
                          min="0"
                          value={quizForm.time_limit}
                          onChange={(e) => setQuizForm({ ...quizForm, time_limit: parseInt(e.target.value) || 0 })}
                        />
                        <Form.Text className="text-muted">
                          Set to 0 for no time limit
                        </Form.Text>
                      </Form.Group>
                    </Col>
                    <Col md={4}>
                      <Form.Group className="mb-3">
                        <Form.Check
                          type="checkbox"
                          label="Published"
                          checked={quizForm.is_published}
                          onChange={(e) => setQuizForm({ ...quizForm, is_published: e.target.checked })}
                          className="mt-4"
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  {/* Quiz Attempt Settings */}
                  <hr className="my-4" />
                  <h6 className="mb-3">Cài đặt lượt thi</h6>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Số lần thi tối đa</Form.Label>
                        <Form.Control
                          type="number"
                          min="0"
                          value={quizForm.max_attempts}
                          onChange={(e) => setQuizForm({ ...quizForm, max_attempts: parseInt(e.target.value) || 0 })}
                        />
                        <Form.Text className="text-muted">
                          Đặt 0 để không giới hạn số lần thi
                        </Form.Text>
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <div className="mt-4">
                        <Form.Check
                          type="checkbox"
                          label="Cho phép xem kết quả"
                          checked={quizForm.show_results}
                          onChange={(e) => setQuizForm({ ...quizForm, show_results: e.target.checked })}
                          className="mb-2"
                        />
                        <Form.Check
                          type="checkbox"
                          label="Hiển thị đáp án đúng"
                          checked={quizForm.show_correct_answer}
                          onChange={(e) => setQuizForm({ ...quizForm, show_correct_answer: e.target.checked })}
                          className="mb-2"
                        />
                        <Form.Check
                          type="checkbox"
                          label="Cho phép xem lịch sử thi"
                          checked={quizForm.allow_review}
                          onChange={(e) => setQuizForm({ ...quizForm, allow_review: e.target.checked })}
                          className="mb-2"
                        />
                      </div>
                    </Col>
                  </Row>

                  <Button
                    variant="primary"
                    onClick={handleSaveQuiz}
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save size={16} className="me-2" />
                        Save Quiz Settings
                      </>
                    )}
                  </Button>
                </Form>
              </Card.Body>
            </Card>
          )}

          {activeTab === 'rules' && (
            /* Quiz Rules */
            <Card>
              <Card.Header>
                <h5 className="mb-0">Quy tắc tạo câu hỏi động</h5>
              </Card.Header>
              <Card.Body>
                <Alert variant="info" className="mb-4">
                  <h6>Hướng dẫn:</h6>
                  <ul className="mb-0">
                    <li>Tạo quy tắc để tự động chọn câu hỏi từ ngân hàng</li>
                    <li>Mỗi lượt thi sẽ có bộ câu hỏi khác nhau nhưng cùng độ khó</li>
                    <li>Đảm bảo ngân hàng câu hỏi có đủ câu hỏi theo điều kiện</li>
                  </ul>
                </Alert>
                <QuizRulesList
                  quizId={parseInt(quizId!)}
                  totalQuestions={quiz?.total_questions || 10}
                  onRulesUpdate={handleRulesUpdate}
                />
              </Card.Body>
            </Card>
          )}

        </Col>

        <Col lg={4}>
          <Card>
            <Card.Header>
              <h6 className="mb-0">Thông tin Quiz</h6>
            </Card.Header>
            <Card.Body>
              <div className="stat-item mb-3">
                <div className="d-flex justify-content-between">
                  <span className="text-muted">Tổng số câu hỏi:</span>
                  <strong>{quiz?.total_questions || 10} câu</strong>
                </div>
              </div>
              <div className="stat-item mb-3">
                <div className="d-flex justify-content-between">
                  <span className="text-muted">Câu hỏi từ rules:</span>
                  <strong>
                    {rulesValidation ? rulesValidation.total_questions : '0'} câu
                  </strong>
                </div>
              </div>
              <div className="stat-item mb-3">
                <div className="d-flex justify-content-between">
                  <span className="text-muted">Trạng thái quy tắc:</span>
                  <Badge bg={rulesValidation?.valid ? 'success' : 'warning'}>
                    {rulesValidation?.valid ? 'Hợp lệ' : 'Cần kiểm tra'}
                  </Badge>
                </div>
              </div>
              <div className="stat-item mb-3">
                <div className="d-flex justify-content-between">
                  <span className="text-muted">Thời gian:</span>
                  <strong>{quiz?.time_limit ? `${quiz.time_limit} phút` : 'Không giới hạn'}</strong>
                </div>
              </div>
              <div className="stat-item mb-3">
                <div className="d-flex justify-content-between">
                  <span className="text-muted">Điểm đạt:</span>
                  <strong>{quiz?.pass_score}%</strong>
                </div>
              </div>
              <div className="stat-item mb-3">
                <div className="d-flex justify-content-between">
                  <span className="text-muted">Trạng thái:</span>
                  <Badge bg={quiz?.is_published ? 'success' : 'warning'}>
                    {quiz?.is_published ? 'Đã xuất bản' : 'Bản nháp'}
                  </Badge>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Quiz Rules are now managed through QuizRulesList component */}
    </Container>
  );
};

export default QuizEdit;
