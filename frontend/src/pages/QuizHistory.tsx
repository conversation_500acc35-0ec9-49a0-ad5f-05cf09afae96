import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Card, Table, Badge, <PERSON><PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';
import { Eye, Clock, CheckCircle, XCircle, BarChart3 } from 'lucide-react';
import { getUserQuizHistory } from '../services/quizHistoryService';
import { type QuizHistory } from '../types';

const QuizHistoryPage: React.FC = () => {
  const { quizId } = useParams<{ quizId: string }>();
  const navigate = useNavigate();
  const [history, setHistory] = useState<QuizHistory | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (quizId) {
      loadHistory();
    }
  }, [quizId]);

  const loadHistory = async () => {
    try {
      setLoading(true);
      const data = await getUserQuizHistory(parseInt(quizId!));
      setHistory(data);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load quiz history');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const getStatusBadge = (attempt: any) => {
    if (!attempt.completed_at) {
      return <Badge bg="warning">Đang làm</Badge>;
    }
    return attempt.is_passed ?
      <Badge bg="success"><CheckCircle size={14} className="me-1" />Đạt</Badge> :
      <Badge bg="danger"><XCircle size={14} className="me-1" />Không đạt</Badge>;
  };

  const handleViewDetail = (attemptId: number) => {
    navigate(`/quiz-attempt/${attemptId}/detail`);
  };

  if (loading) {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <Spinner animation="border" />
          <p className="mt-2">Đang tải lịch sử thi...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="mt-4">
        <Alert variant="danger">{error}</Alert>
      </Container>
    );
  }

  if (!history) {
    return (
      <Container className="mt-4">
        <Alert variant="info">Không tìm thấy lịch sử thi</Alert>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <Card>
        <Card.Header>
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h4 className="mb-1">
                <BarChart3 className="me-2" />
                Lịch sử thi: {history.quiz.title}
              </h4>
              <div className="text-muted small">
                {history.quiz.max_attempts > 0 && (
                  <span className="me-3">
                    Giới hạn: {history.quiz.max_attempts} lần
                  </span>
                )}
                <span className="me-3">
                  Đã thi: {history.attempts.length} lần
                </span>
                {history.quiz.show_results && (
                  <Badge bg="info" className="me-2">Hiển thị kết quả</Badge>
                )}
                {history.quiz.show_correct_answer && (
                  <Badge bg="success" className="me-2">Hiển thị đáp án</Badge>
                )}
              </div>
            </div>
            <Button variant="outline-secondary" onClick={() => navigate(-1)}>
              Quay lại
            </Button>
          </div>
        </Card.Header>

        <Card.Body>
          {history.attempts.length === 0 ? (
            <Alert variant="info">
              Bạn chưa có lần thi nào cho quiz này.
            </Alert>
          ) : (
            <Table responsive hover>
              <thead>
                <tr>
                  <th>Lần thi</th>
                  <th>Thời gian bắt đầu</th>
                  <th>Thời gian hoàn thành</th>
                  <th>Trạng thái</th>
                  {history.quiz.show_results && (
                    <>
                      <th>Điểm</th>
                      <th>Tỷ lệ</th>
                    </>
                  )}
                  <th>Thao tác</th>
                </tr>
              </thead>
              <tbody>
                {history.attempts.map((attempt, index) => (
                  <tr key={attempt.id}>
                    <td>
                      <Badge bg="secondary">#{history.attempts.length - index}</Badge>
                    </td>
                    <td>
                      <Clock size={14} className="me-1" />
                      {formatDate(attempt.started_at)}
                    </td>
                    <td>
                      {attempt.completed_at ? (
                        formatDate(attempt.completed_at)
                      ) : (
                        <span className="text-muted">Chưa hoàn thành</span>
                      )}
                    </td>
                    <td>{getStatusBadge(attempt)}</td>
                    {history.quiz.show_results && (
                      <>
                        <td>
                          {attempt.completed_at ? (
                            <strong>{attempt.score.toFixed(1)}/{attempt.max_score.toFixed(1)}</strong>
                          ) : (
                            <span className="text-muted">-</span>
                          )}
                        </td>
                        <td>
                          {attempt.completed_at ? (
                            <Badge
                              bg={attempt.is_passed ? 'success' : 'danger'}
                              className="fs-6"
                            >
                              {attempt.percentage.toFixed(1)}%
                            </Badge>
                          ) : (
                            <span className="text-muted">-</span>
                          )}
                        </td>
                      </>
                    )}
                    <td>
                      {attempt.completed_at && (history.quiz.show_results || history.quiz.show_correct_answer) && (
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => handleViewDetail(attempt.id)}
                        >
                          <Eye size={14} className="me-1" />
                          Xem chi tiết
                        </Button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default QuizHistoryPage;
