import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Button, Row, <PERSON>, Spin<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Form } from 'react-bootstrap';
import { Plus, RefreshCw, BarChart3 } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';
import { quizService, type CreateQuizRequest } from '../services/quizService';
import { courseService } from '../services/courseService';
import { type Quiz, type Course } from '../types';
import { QuizCard } from '../components';

const Quizzes: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useAuth();
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);

  const isTeacherOrAdmin = state.user?.role === 'teacher' || state.user?.role === 'admin';

  const [newQuiz, setNewQuiz] = useState<CreateQuizRequest>({
    course_id: 0,
    title: '',
    description: '',
    time_limit: 0,
    pass_score: 70,
    total_questions: 10,

    // Quiz attempt settings
    max_attempts: 0,
    show_results: true,
    show_correct_answer: false,
    allow_review: true,
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch courses first
      const coursesData = await courseService.getCourses();
      setCourses(coursesData);

      // Fetch all quizzes from all courses
      const allQuizzes: Quiz[] = [];
      for (const course of coursesData) {
        try {
          const courseQuizzes = await quizService.getQuizzesByCourse(course.id);
          allQuizzes.push(...courseQuizzes);
        } catch (err) {
          console.error(`Error fetching quizzes for course ${course.id}:`, err);
        }
      }
      setQuizzes(allQuizzes);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateQuiz = async () => {
    if (!newQuiz.course_id || !newQuiz.title.trim()) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setCreateLoading(true);
      await quizService.createQuiz(newQuiz);
      setShowCreateModal(false);
      setNewQuiz({
        course_id: 0,
        title: '',
        description: '',
        time_limit: 0,
        pass_score: 70,
        total_questions: 10,

        // Quiz attempt settings
        max_attempts: 0,
        show_results: true,
        show_correct_answer: false,
        allow_review: true,
      });
      await fetchData();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to create quiz');
    } finally {
      setCreateLoading(false);
    }
  };

  const handleDeleteQuiz = async (quizId: number) => {
    if (!window.confirm('Are you sure you want to delete this quiz?')) {
      return;
    }

    try {
      await quizService.deleteQuiz(quizId);
      await fetchData();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete quiz');
    }
  };

  const handleTakeQuiz = (quizId: number) => {
    navigate(`/quizzes/${quizId}/attempt`);
  };

  const handleEditQuiz = (quiz: Quiz) => {
    navigate(`/quizzes/${quiz.id}/edit`);
  };

  const handleViewHistory = (quizId: number) => {
    navigate(`/quizzes/${quizId}/history`);
  };

  const getCourseTitle = (courseId: number) => {
    const course = courses.find(c => c.id === courseId);
    return course?.title || 'Unknown Course';
  };

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>Quizzes</h2>
        <div className="d-flex gap-2">
          <Button
            variant="outline-secondary"
            onClick={fetchData}
            disabled={loading}
          >
            <RefreshCw size={16} className={`me-2 ${loading ? 'spin' : ''}`} />
            Refresh
          </Button>
          {isTeacherOrAdmin && (
            <Button variant="primary" onClick={() => setShowCreateModal(true)}>
              <Plus size={16} className="me-2" />
              Create Quiz
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <strong>Error:</strong> {error}
          <Button
            variant="link"
            className="p-0 ms-2"
            onClick={() => setError(null)}
          >
            Dismiss
          </Button>
        </Alert>
      )}

      {loading ? (
        <div className="text-center py-5">
          <Spinner animation="border" role="status" className="mb-3">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <p className="text-muted">Loading quizzes...</p>
        </div>
      ) : quizzes.length === 0 ? (
        <Card>
          <Card.Body className="text-center py-5">
            <BarChart3 size={64} className="text-muted mb-3" />
            <h4>No Quizzes Found</h4>
            <p className="text-muted">
              {isTeacherOrAdmin
                ? "Get started by creating your first quiz!"
                : "No quizzes are available at the moment."
              }
            </p>
            {isTeacherOrAdmin && (
              <Button variant="primary" onClick={() => setShowCreateModal(true)}>
                <Plus size={16} className="me-2" />
                Create Your First Quiz
              </Button>
            )}
          </Card.Body>
        </Card>
      ) : (
        <Row>
          {quizzes.map((quiz) => (
            <Col key={quiz.id} lg={4} md={6} className="mb-4">
              <QuizCard
                quiz={quiz}
                courseTitle={getCourseTitle(quiz.course_id)}
                isTeacherOrAdmin={isTeacherOrAdmin}
                onTakeQuiz={handleTakeQuiz}
                onEditQuiz={handleEditQuiz}
                onDeleteQuiz={handleDeleteQuiz}
                onViewHistory={handleViewHistory}
              />
            </Col>
          ))}
        </Row>
      )}

      {/* Create Quiz Modal */}
      <Modal show={showCreateModal} onHide={() => setShowCreateModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Create New Quiz</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Course *</Form.Label>
              <Form.Select
                value={newQuiz.course_id}
                onChange={(e) => setNewQuiz({ ...newQuiz, course_id: parseInt(e.target.value) })}
                required
              >
                <option value={0}>Select a course...</option>
                {courses.map((course) => (
                  <option key={course.id} value={course.id}>
                    {course.title}
                  </option>
                ))}
              </Form.Select>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Quiz Title *</Form.Label>
              <Form.Control
                type="text"
                value={newQuiz.title}
                onChange={(e) => setNewQuiz({ ...newQuiz, title: e.target.value })}
                placeholder="Enter quiz title"
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={newQuiz.description}
                onChange={(e) => setNewQuiz({ ...newQuiz, description: e.target.value })}
                placeholder="Enter quiz description"
              />
            </Form.Group>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Total Questions</Form.Label>
                  <Form.Control
                    type="number"
                    min="1"
                    max="100"
                    value={newQuiz.total_questions}
                    onChange={(e) => setNewQuiz({ ...newQuiz, total_questions: parseInt(e.target.value) || 10 })}
                  />
                  <Form.Text className="text-muted">
                    Number of questions to generate
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Time Limit (minutes)</Form.Label>
                  <Form.Control
                    type="number"
                    min="0"
                    value={newQuiz.time_limit}
                    onChange={(e) => setNewQuiz({ ...newQuiz, time_limit: parseInt(e.target.value) || 0 })}
                    placeholder="0 = No limit"
                  />
                  <Form.Text className="text-muted">
                    Set to 0 for no time limit
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Pass Score (%)</Form.Label>
                  <Form.Control
                    type="number"
                    min="0"
                    max="100"
                    value={newQuiz.pass_score}
                    onChange={(e) => setNewQuiz({ ...newQuiz, pass_score: parseInt(e.target.value) || 70 })}
                  />
                </Form.Group>
              </Col>
            </Row>

            {/* Quiz Attempt Settings */}
            <hr className="my-4" />
            <h6 className="mb-3">Quiz Attempt Settings</h6>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Max Attempts</Form.Label>
                  <Form.Control
                    type="number"
                    min="0"
                    value={newQuiz.max_attempts}
                    onChange={(e) => setNewQuiz({ ...newQuiz, max_attempts: parseInt(e.target.value) || 0 })}
                  />
                  <Form.Text className="text-muted">
                    Set to 0 for unlimited attempts
                  </Form.Text>
                </Form.Group>
              </Col>
              <Col md={6}>
                <div className="mt-4">
                  <Form.Check
                    type="checkbox"
                    label="Show Results"
                    checked={newQuiz.show_results}
                    onChange={(e) => setNewQuiz({ ...newQuiz, show_results: e.target.checked })}
                    className="mb-2"
                  />
                  <Form.Check
                    type="checkbox"
                    label="Show Correct Answers"
                    checked={newQuiz.show_correct_answer}
                    onChange={(e) => setNewQuiz({ ...newQuiz, show_correct_answer: e.target.checked })}
                    className="mb-2"
                  />
                  <Form.Check
                    type="checkbox"
                    label="Allow Review"
                    checked={newQuiz.allow_review}
                    onChange={(e) => setNewQuiz({ ...newQuiz, allow_review: e.target.checked })}
                    className="mb-2"
                  />
                </div>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCreateModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleCreateQuiz}
            disabled={createLoading || !newQuiz.course_id || !newQuiz.title.trim()}
          >
            {createLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Creating...
              </>
            ) : (
              'Create Quiz'
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default Quizzes;
