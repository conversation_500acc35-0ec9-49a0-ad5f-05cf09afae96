import React, { useState } from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import SlideEditor from '../components/SlideEditor';

const SlideEditorDemo: React.FC = () => {
  const navigate = useNavigate();
  const [savedSlide, setSavedSlide] = useState<{ title: string; content: string } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSave = (title: string, content: string) => {
    // Simulate saving
    console.log('Saving slide:', { title, content });
    setSavedSlide({ title, content });
    setError(null);
    
    // Show success message
    setTimeout(() => {
      alert('Slide saved successfully! (This is a demo)');
    }, 500);
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const initialContent = `# Welcome to Slide Editor Demo

This is a demonstration of the **Markdown Slide Editor** with real-time preview.

## Features

### ✨ Real-time Preview
- See your changes instantly as you type
- Split view with editor and preview side by side
- Toggle between edit-only, preview-only, and split modes

### 📝 Markdown Support
- **Bold** and *italic* text
- Headers (H1, H2, H3, etc.)
- Lists and numbered lists
- Code blocks and inline \`code\`
- Links and images
- Tables
- Blockquotes

### 🎯 Code Examples

\`\`\`javascript
function createSlide(title, content) {
  return {
    title: title,
    content: content,
    timestamp: new Date()
  };
}
\`\`\`

### 📊 Tables

| Feature | Status | Notes |
|---------|--------|-------|
| Markdown Editor | ✅ | Full featured |
| Live Preview | ✅ | Real-time updates |
| Save/Load | ✅ | Backend integration |
| Image Support | ✅ | URL-based images |

### 💡 Tips

> **Pro Tip**: Use the toolbar buttons to switch between different view modes:
> - **Edit**: Focus on writing
> - **Split**: See both editor and preview
> - **Preview**: See final result

### 🔗 Links and Images

You can add links like [this link to Google](https://google.com).

For images, use the image URL field above or markdown syntax:
![Sample Image](https://via.placeholder.com/400x200/0066cc/ffffff?text=Sample+Slide+Image)

---

**Happy slide creating!** 🚀`;

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Container fluid className="bg-light border-bottom py-3">
        <Row>
          <Col>
            <div className="d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <Button 
                  variant="outline-secondary" 
                  size="sm" 
                  onClick={handleCancel}
                  className="me-3"
                >
                  <ArrowLeft size={16} className="me-1" />
                  Back to Dashboard
                </Button>
                <div>
                  <h4 className="mb-0">Slide Editor Demo</h4>
                  <small className="text-muted">
                    Test the Markdown editor with real-time preview
                  </small>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Container>

      {/* Success Message */}
      {savedSlide && (
        <Container fluid className="py-2">
          <Alert variant="success" className="mb-0">
            <strong>Demo Save Successful!</strong> Title: "{savedSlide.title}" | Content length: {savedSlide.content.length} characters
          </Alert>
        </Container>
      )}

      {/* Editor */}
      <div className="flex-grow-1">
        <SlideEditor
          initialTitle="Demo Slide: Markdown Editor Features"
          initialContent={initialContent}
          onSave={handleSave}
          onCancel={handleCancel}
          error={error}
        />
      </div>
    </div>
  );
};

export default SlideEditorDemo;
