import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, Card, <PERSON>, Badge, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { BookOpen, TrendingUp, Clock, Award, Play, History, Edit } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { getUserQuizHistory } from '../services/quizHistoryService';
import { quizService } from '../services/quizService';
import { courseService } from '../services/courseService';
import type { Quiz, Course, QuizHistory } from '../types';

const UserDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Data states
  const [recentQuizzes, setRecentQuizzes] = useState<Quiz[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [myQuizHistory, setMyQuizHistory] = useState<QuizHistory[]>([]);
  
  // Stats
  const [stats, setStats] = useState({
    totalAttempts: 0,
    passedAttempts: 0,
    averageScore: 0,
    coursesEnrolled: 0
  });

  const isTeacherOrAdmin = state.user?.role === 'teacher' || state.user?.role === 'admin';

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load courses
      const coursesData = await courseService.getCourses();
      setCourses(coursesData);
      
      // Load recent quizzes from all courses
      const allQuizzes: Quiz[] = [];
      for (const course of coursesData) {
        try {
          const courseQuizzes = await quizService.getQuizzesByCourse(course.id);
          allQuizzes.push(...courseQuizzes);
        } catch (err) {
          console.warn(`Failed to load quizzes for course ${course.id}`);
        }
      }
      
      // Sort by created date and take recent ones
      const sortedQuizzes = allQuizzes
        .filter(quiz => quiz.is_published)
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5);
      
      setRecentQuizzes(sortedQuizzes);
      
      // Load quiz history for current user (if available)
      const historyPromises = sortedQuizzes.map(async (quiz) => {
        try {
          return await getUserQuizHistory(quiz.id);
        } catch (err) {
          return null;
        }
      });
      
      const historyResults = await Promise.all(historyPromises);
      const validHistory = historyResults.filter(h => h !== null) as QuizHistory[];
      setMyQuizHistory(validHistory);
      
      // Calculate stats
      const totalAttempts = validHistory.reduce((sum, h) => sum + h.attempts.length, 0);
      const passedAttempts = validHistory.reduce((sum, h) => 
        sum + h.attempts.filter(a => a.is_passed).length, 0
      );
      const allScores = validHistory.flatMap(h => h.attempts.map(a => a.percentage));
      const averageScore = allScores.length > 0 ? 
        allScores.reduce((sum, score) => sum + score, 0) / allScores.length : 0;
      
      setStats({
        totalAttempts,
        passedAttempts,
        averageScore,
        coursesEnrolled: coursesData.length
      });
      
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getPassRateColor = (passRate: number) => {
    if (passRate >= 80) return 'success';
    if (passRate >= 60) return 'warning';
    return 'danger';
  };

  const getCourseTitle = (courseId: number) => {
    const course = courses.find(c => c.id === courseId);
    return course?.title || 'Unknown Course';
  };

  if (loading) {
    return (
      <Container className="mt-4">
        <div className="text-center">
          <Spinner animation="border" />
          <p className="mt-2">Đang tải dashboard...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="mt-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1>
            <TrendingUp className="me-2" />
            Dashboard
          </h1>
          <p className="text-muted mb-0">
            Chào mừng trở lại, {state.user?.first_name} {state.user?.last_name}!
          </p>
        </div>
        {isTeacherOrAdmin && (
          <Button 
            variant="outline-primary" 
            onClick={() => navigate('/admin-dashboard')}
          >
            Admin Dashboard
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
          <Button variant="link" className="p-0 ms-2" onClick={() => setError(null)}>
            Dismiss
          </Button>
        </Alert>
      )}

      {/* Stats Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center h-100">
            <Card.Body>
              <BookOpen size={32} className="text-primary mb-2" />
              <h4>{stats.coursesEnrolled}</h4>
              <small className="text-muted">Khóa học</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100">
            <Card.Body>
              <Play size={32} className="text-info mb-2" />
              <h4>{stats.totalAttempts}</h4>
              <small className="text-muted">Lượt thi</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100">
            <Card.Body>
              <Award size={32} className="text-success mb-2" />
              <h4>{stats.passedAttempts}</h4>
              <small className="text-muted">Đạt yêu cầu</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center h-100">
            <Card.Body>
              <TrendingUp size={32} className="text-warning mb-2" />
              <h4>{stats.averageScore.toFixed(1)}%</h4>
              <small className="text-muted">Điểm trung bình</small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        {/* Recent Quizzes */}
        <Col lg={8}>
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Quiz gần đây</h5>
            </Card.Header>
            <Card.Body>
              {recentQuizzes.length === 0 ? (
                <div className="text-center py-4">
                  <BookOpen size={48} className="text-muted mb-3" />
                  <p className="text-muted">Chưa có quiz nào</p>
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Quiz</th>
                      <th>Khóa học</th>
                      <th>Câu hỏi</th>
                      <th>Thời gian</th>
                      <th>Thao tác</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentQuizzes.map((quiz) => {
                      const myHistory = myQuizHistory.find(h => h.quiz.id === quiz.id);
                      const hasAttempted = myHistory && myHistory.attempts.length > 0;
                      const lastAttempt = hasAttempted ? 
                        myHistory.attempts.sort((a, b) => 
                          new Date(b.completed_at).getTime() - new Date(a.completed_at).getTime()
                        )[0] : null;

                      return (
                        <tr key={quiz.id}>
                          <td>
                            <div>
                              <strong>{quiz.title}</strong>
                              {lastAttempt && (
                                <div>
                                  <Badge bg={lastAttempt.is_passed ? 'success' : 'danger'} className="me-1">
                                    {lastAttempt.percentage.toFixed(1)}%
                                  </Badge>
                                  <small className="text-muted">
                                    Lần cuối: {formatDate(lastAttempt.completed_at)}
                                  </small>
                                </div>
                              )}
                            </div>
                          </td>
                          <td>{getCourseTitle(quiz.course_id)}</td>
                          <td>
                            <Badge bg="info">{quiz.total_questions}</Badge>
                          </td>
                          <td>
                            {quiz.time_limit > 0 ? (
                              <Badge bg="secondary">
                                <Clock size={12} className="me-1" />
                                {quiz.time_limit}p
                              </Badge>
                            ) : (
                              <Badge bg="outline-secondary">Không giới hạn</Badge>
                            )}
                          </td>
                          <td>
                            <div className="d-flex gap-1">
                              <Button
                                variant="outline-primary"
                                size="sm"
                                onClick={() => navigate(`/quizzes/${quiz.id}/attempt`)}
                              >
                                <Play size={14} className="me-1" />
                                Thi
                              </Button>
                              {hasAttempted && quiz.allow_review && (
                                <Button
                                  variant="outline-info"
                                  size="sm"
                                  onClick={() => navigate(`/quizzes/${quiz.id}/history`)}
                                >
                                  <History size={14} className="me-1" />
                                  Lịch sử
                                </Button>
                              )}
                              {isTeacherOrAdmin && (
                                <Button
                                  variant="outline-secondary"
                                  size="sm"
                                  onClick={() => navigate(`/quizzes/${quiz.id}/edit`)}
                                >
                                  <Edit size={14} className="me-1" />
                                  Sửa
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Quick Actions & Recent Activity */}
        <Col lg={4}>
          <Card className="mb-4">
            <Card.Header>
              <h6 className="mb-0">Thao tác nhanh</h6>
            </Card.Header>
            <Card.Body>
              <div className="d-grid gap-2">
                <Button variant="primary" onClick={() => navigate('/courses')}>
                  <BookOpen size={16} className="me-2" />
                  Xem khóa học
                </Button>
                <Button variant="outline-primary" onClick={() => navigate('/quizzes')}>
                  <Play size={16} className="me-2" />
                  Tất cả Quiz
                </Button>
                {isTeacherOrAdmin && (
                  <>
                    <Button variant="outline-secondary" onClick={() => navigate('/question-bank')}>
                      <Edit size={16} className="me-2" />
                      Ngân hàng câu hỏi
                    </Button>
                    <Button variant="outline-info" onClick={() => navigate('/admin-dashboard')}>
                      <TrendingUp size={16} className="me-2" />
                      Admin Dashboard
                    </Button>
                  </>
                )}
              </div>
            </Card.Body>
          </Card>

          {/* Performance Summary */}
          {stats.totalAttempts > 0 && (
            <Card>
              <Card.Header>
                <h6 className="mb-0">Tóm tắt kết quả</h6>
              </Card.Header>
              <Card.Body>
                <div className="mb-3">
                  <div className="d-flex justify-content-between">
                    <span>Tỷ lệ đạt</span>
                    <span>{stats.totalAttempts > 0 ? ((stats.passedAttempts / stats.totalAttempts) * 100).toFixed(1) : 0}%</span>
                  </div>
                  <div className="progress mt-1" style={{ height: '8px' }}>
                    <div 
                      className={`progress-bar bg-${getPassRateColor((stats.passedAttempts / stats.totalAttempts) * 100)}`}
                      style={{ width: `${(stats.passedAttempts / stats.totalAttempts) * 100}%` }}
                    />
                  </div>
                </div>
                
                <div className="small">
                  <div className="d-flex justify-content-between mb-1">
                    <span>Điểm cao nhất</span>
                    <span className="text-success">
                      {myQuizHistory.length > 0 ? 
                        Math.max(...myQuizHistory.flatMap(h => h.attempts.map(a => a.percentage))).toFixed(1) : 0}%
                    </span>
                  </div>
                  <div className="d-flex justify-content-between">
                    <span>Quiz đã hoàn thành</span>
                    <span>{myQuizHistory.filter(h => h.attempts.some(a => a.is_passed)).length}</span>
                  </div>
                </div>
              </Card.Body>
            </Card>
          )}
        </Col>
      </Row>
    </Container>
  );
};

export default UserDashboard;
