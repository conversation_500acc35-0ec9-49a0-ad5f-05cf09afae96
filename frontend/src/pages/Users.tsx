import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Bad<PERSON>,
  Spinner,
  <PERSON><PERSON>,
  Dropdown,
  Modal,
  Row,
  Col
} from 'react-bootstrap';
import {
  UserPlus,
  Edit,
  Trash2,
  RefreshCw,
  MoreVertical,
  Users as UsersIcon,
  Shield,
  GraduationCap,
  User as UserIcon
} from 'lucide-react';
import { useUsers } from '../hooks/useUsers';
import { useAuth } from '../hooks/useAuth';
import { type User } from '../types';
import { UserForm } from '../components';
import { type CreateUserRequest, type UpdateUserRequest } from '../services/userService';

const Users: React.FC = () => {
  const { users, loading, error, refetch, createUser, updateUser, deleteUser } = useUsers();
  const { state } = useAuth();

  const [showUserForm, setShowUserForm] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingUser, setDeletingUser] = useState<User | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  const handleCreateUser = () => {
    setEditingUser(null);
    setFormError(null);
    setShowUserForm(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setFormError(null);
    setShowUserForm(true);
  };

  const handleDeleteUser = (user: User) => {
    setDeletingUser(user);
    setShowDeleteModal(true);
  };

  const handleFormSubmit = async (userData: CreateUserRequest | UpdateUserRequest) => {
    try {
      setFormError(null);
      if (editingUser) {
        await updateUser(editingUser.id, userData as UpdateUserRequest);
      } else {
        await createUser(userData as CreateUserRequest);
      }
    } catch (error: any) {
      setFormError(error.message);
      throw error;
    }
  };

  const confirmDelete = async () => {
    if (!deletingUser) return;

    try {
      await deleteUser(deletingUser.id);
      setShowDeleteModal(false);
      setDeletingUser(null);
    } catch (error) {
      // Error is handled by useUsers hook
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield size={16} className="me-1" />;
      case 'teacher':
        return <GraduationCap size={16} className="me-1" />;
      case 'student':
        return <UserIcon size={16} className="me-1" />;
      default:
        return <UserIcon size={16} className="me-1" />;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'danger';
      case 'teacher':
        return 'primary';
      case 'student':
        return 'secondary';
      default:
        return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const isCurrentUser = (user: User) => {
    return state.user?.id === user.id;
  };

  return (
    <div>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2>User Management</h2>
        <div className="d-flex gap-2">
          <Button
            variant="outline-secondary"
            onClick={refetch}
            disabled={loading}
          >
            <RefreshCw size={16} className={`me-2 ${loading ? 'spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="primary" onClick={handleCreateUser}>
            <UserPlus size={16} className="me-2" />
            Add User
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="danger" className="mb-4">
          <strong>Error:</strong> {error}
          <Button
            variant="link"
            className="p-0 ms-2"
            onClick={refetch}
          >
            Try again
          </Button>
        </Alert>
      )}

      {/* Stats Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center user-stats-card">
            <Card.Body>
              <UsersIcon size={32} className="text-primary mb-2" />
              <h4>{users.length}</h4>
              <small className="text-muted">Total Users</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center user-stats-card">
            <Card.Body>
              <Shield size={32} className="text-danger mb-2" />
              <h4>{users.filter(u => u.role === 'admin').length}</h4>
              <small className="text-muted">Admins</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center user-stats-card">
            <Card.Body>
              <GraduationCap size={32} className="text-primary mb-2" />
              <h4>{users.filter(u => u.role === 'teacher').length}</h4>
              <small className="text-muted">Teachers</small>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center user-stats-card">
            <Card.Body>
              <UserIcon size={32} className="text-secondary mb-2" />
              <h4>{users.filter(u => u.role === 'student').length}</h4>
              <small className="text-muted">Students</small>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Users Table */}
      <Card className="user-table">
        <Card.Header>
          <h5 className="mb-0">All Users</h5>
        </Card.Header>
        <Card.Body className="p-0">
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" role="status" className="mb-3">
                <span className="visually-hidden">Loading...</span>
              </Spinner>
              <p className="text-muted">Loading users...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-5">
              <UsersIcon size={64} className="text-muted mb-3" />
              <h4>No Users Found</h4>
              <p className="text-muted">Get started by creating your first user!</p>
              <Button variant="primary" onClick={handleCreateUser}>
                <UserPlus size={16} className="me-2" />
                Create First User
              </Button>
            </div>
          ) : (
            <Table responsive hover className="mb-0">
              <thead className="table-light">
                <tr>
                  <th>User</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Status</th>
                  <th>Created</th>
                  <th style={{ width: '100px' }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id}>
                    <td>
                      <div className="d-flex align-items-center">
                        <div className="bg-light rounded-circle d-flex align-items-center justify-content-center me-3"
                          style={{ width: '40px', height: '40px' }}>
                          {getRoleIcon(user.role)}
                        </div>
                        <div>
                          <div className="fw-medium">
                            {user.first_name} {user.last_name}
                            {isCurrentUser(user) && (
                              <Badge bg="info" className="ms-2">You</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>{user.email}</td>
                    <td>
                      <Badge bg={getRoleBadgeVariant(user.role)}>
                        {getRoleIcon(user.role)}
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </Badge>
                    </td>
                    <td>
                      <Badge bg={user.is_active ? 'success' : 'warning'}>
                        {user.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td>{formatDate(user.created_at)}</td>
                    <td>
                      <Dropdown>
                        <Dropdown.Toggle variant="link" className="btn-sm p-0" style={{ border: 'none' }}>
                          <MoreVertical size={16} />
                        </Dropdown.Toggle>
                        <Dropdown.Menu>
                          <Dropdown.Item onClick={() => handleEditUser(user)}>
                            <Edit size={14} className="me-2" />
                            Edit
                          </Dropdown.Item>
                          {!isCurrentUser(user) && (
                            <Dropdown.Item
                              onClick={() => handleDeleteUser(user)}
                              className="text-danger"
                            >
                              <Trash2 size={14} className="me-2" />
                              Delete
                            </Dropdown.Item>
                          )}
                        </Dropdown.Menu>
                      </Dropdown>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          )}
        </Card.Body>
      </Card>

      {/* User Form Modal */}
      <UserForm
        show={showUserForm}
        onHide={() => setShowUserForm(false)}
        onSubmit={handleFormSubmit}
        user={editingUser}
        error={formError}
      />

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete user <strong>{deletingUser?.first_name} {deletingUser?.last_name}</strong>?
          This action cannot be undone.
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={confirmDelete}>
            Delete User
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default Users;
