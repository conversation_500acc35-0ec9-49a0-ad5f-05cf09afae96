import api from './authService';
import { type Course } from '../types';

export interface CoursesResponse {
  courses: Course[];
}

export interface CourseResponse {
  course: Course;
}

export const courseService = {
  async getCourses(): Promise<Course[]> {
    try {
      const response = await api.get<CoursesResponse>('/courses');
      return response.data.courses || [];
    } catch (error) {
      console.error('Error fetching courses:', error);
      throw error;
    }
  },

  async getCourse(id: number): Promise<Course> {
    try {
      const response = await api.get<CourseResponse>(`/courses/${id}`);
      return response.data.course;
    } catch (error) {
      console.error('Error fetching course:', error);
      throw error;
    }
  },

  async getCourseById(id: number): Promise<Course> {
    return this.getCourse(id);
  },

  async createCourse(courseData: {
    title: string;
    description: string;
    thumbnail?: string;
  }): Promise<Course> {
    try {
      const response = await api.post<CourseResponse>('/courses', courseData);
      return response.data.course;
    } catch (error) {
      console.error('Error creating course:', error);
      throw error;
    }
  },

  async updateCourse(id: number, courseData: {
    title?: string;
    description?: string;
    thumbnail?: string;
    is_published?: boolean;
  }): Promise<Course> {
    try {
      const response = await api.put<CourseResponse>(`/courses/${id}`, courseData);
      return response.data.course;
    } catch (error) {
      console.error('Error updating course:', error);
      throw error;
    }
  },

  async deleteCourse(id: number): Promise<void> {
    try {
      await api.delete(`/courses/${id}`);
    } catch (error) {
      console.error('Error deleting course:', error);
      throw error;
    }
  },
};
