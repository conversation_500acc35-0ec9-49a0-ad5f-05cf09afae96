import api from './authService';
import type {
  QuizHistory,
  QuizAttemptDetail,
  QuizStats,
  QuizAttemptsList,
  UserQuizSummary,
  AllQuizzesSummary
} from '../types';

// User quiz history services
export const getUserQuizHistory = async (quizId: number): Promise<QuizHistory> => {
  const response = await api.get(`/quizzes/${quizId}/history`);
  return response.data;
};

export const getQuizAttemptDetail = async (attemptId: number): Promise<QuizAttemptDetail> => {
  const response = await api.get(`/quizzes/attempt/${attemptId}/detail`);
  return response.data;
};

// Admin quiz management services
export const getQuizStats = async (quizId: number): Promise<QuizStats> => {
  const response = await api.get(`/quizzes/${quizId}/stats`);
  return response.data;
};

export const getQuizAttemptsList = async (
  quizId: number,
  page: number = 1,
  limit: number = 20,
  status?: 'passed' | 'failed'
): Promise<QuizAttemptsList> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (status) {
    params.append('status', status);
  }

  const response = await api.get(`/quizzes/${quizId}/attempts?${params}`);
  return response.data;
};

export const getUserQuizSummary = async (userId: number): Promise<UserQuizSummary> => {
  const response = await api.get(`/admin/users/${userId}/quiz-summary`);
  return response.data;
};

export const getAllQuizzesSummary = async (): Promise<AllQuizzesSummary> => {
  const response = await api.get('/quizzes/summary');
  return response.data;
};
