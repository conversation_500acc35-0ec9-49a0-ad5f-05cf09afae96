import axios from 'axios';
import { type User } from '../types';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';

export interface CreateUserRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'teacher' | 'student';
}

export interface UpdateUserRequest {
  email: string;
  first_name: string;
  last_name: string;
  role: 'admin' | 'teacher' | 'student';
  is_active: boolean;
}

export interface UpdateUserRoleRequest {
  role: 'admin' | 'teacher' | 'student';
}

export interface UpdateUserStatusRequest {
  is_active: boolean;
}

class UserService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    };
  }

  async getUsers(): Promise<User[]> {
    try {
      const response = await axios.get(`${API_URL}/admin/users`, this.getAuthHeaders());
      return response.data.users;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  async createUser(userData: CreateUserRequest): Promise<User> {
    try {
      const response = await axios.post(`${API_URL}/admin/users`, userData, this.getAuthHeaders());
      return response.data.user;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  async updateUser(userId: number, userData: UpdateUserRequest): Promise<User> {
    try {
      const response = await axios.put(`${API_URL}/admin/users/${userId}`, userData, this.getAuthHeaders());
      return response.data.user;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  async deleteUser(userId: number): Promise<void> {
    try {
      await axios.delete(`${API_URL}/admin/users/${userId}`, this.getAuthHeaders());
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  async updateUserRole(userId: number, role: 'admin' | 'teacher' | 'student'): Promise<User> {
    try {
      const response = await axios.put(
        `${API_URL}/admin/users/${userId}/role`,
        { role },
        this.getAuthHeaders()
      );
      return response.data.user;
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  }

  async updateUserStatus(userId: number, isActive: boolean): Promise<User> {
    try {
      const response = await axios.put(
        `${API_URL}/admin/users/${userId}/status`,
        { is_active: isActive },
        this.getAuthHeaders()
      );
      return response.data.user;
    } catch (error) {
      console.error('Error updating user status:', error);
      throw error;
    }
  }
}

export const userService = new UserService();

// Export individual functions for easier importing
export const getUsers = () => userService.getUsers();
export const createUser = (userData: CreateUserRequest) => userService.createUser(userData);
export const updateUser = (userId: number, userData: UpdateUserRequest) => userService.updateUser(userId, userData);
export const deleteUser = (userId: number) => userService.deleteUser(userId);
export const updateUserRole = (userId: number, role: 'admin' | 'teacher' | 'student') => userService.updateUserRole(userId, role);
export const updateUserStatus = (userId: number, isActive: boolean) => userService.updateUserStatus(userId, isActive);
